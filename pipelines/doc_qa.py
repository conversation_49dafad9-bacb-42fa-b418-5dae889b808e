from typing import List, Dict, Any, AsyncGenerator, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from prompts.doc_qa_prompt import doc_qa_strict_empty_sys_prompt, doc_qa_strict_nonempty_sys_prompt, doc_qa_common_empty_sys_prompt, doc_qa_common_nonempty_sys_prompt, doc_qa_strict_nonempty_user_prompt, doc_qa_common_nonempty_user_prompt, doc_qa_strict_empty_user_prompt, doc_qa_common_empty_user_prompt
from services.redis_service import RedisService

from loguru import logger
from config.logging_config import configure_logging
from config.doc_redis_config import DOC_REDIS_CONFIG
configure_logging()
import json


class DocQA:
    """文档问答类，支持流式和非流式输出"""
    
    def __init__(self, model_id: str, request_id: str = None):
        """
        初始化文档问答实例
        
        Args:
            model_id: 模型ID (e.g. "gpt_4o", "qwen3_32b")
            request_id: 可选请求ID
        """
        self.model_id = model_id
        self.request_id = request_id
        # 实例化模型提供者
        self.provider = get_llm_provider(model_id, request_id)
        # 实例化搜索服务
        self.search_service = RedisService(config=DOC_REDIS_CONFIG, request_id=request_id)
        self.logger = logger.bind(request_id=request_id)
        self.logger.info(f"DocQA instance initialized with model_id: {model_id}")
    
    async def _retrieve_knowledge(self, query: str, user_id: str, conversation_id: str):
        """检索文档"""
        # 搜索文档
        self.logger.info(f"开始检索文档, 用户ID: {user_id}, 会话ID: {conversation_id}, 查询: {query}")
        search_results, error = await self.search_service.search(
            conversation_id=conversation_id
        )
        # print(f"检索结果: {search_results}")
        
        if error or not search_results:
            self.logger.info(f"文档检索失败: {error}")
            return ""
        self.logger.info(f"检索到文档: {len(search_results)}")
        return search_results
    
    def format_knowledge(self, search_results):
        document = ''
        for i, doc in enumerate(search_results):
            document += f"第{i+1}篇文档:\n"
            document += f"文档名称: {doc['name']}\n"
            document += doc['content']
        return document
    
    def _build_messages(self, query: str, history: List[Dict], knowledge: str, mode: str) -> List[Dict]:
        """构建OpenAI格式的消息列表（适配新history格式）"""
        print(f"构建消息: query:{query}, knowledge长度:{len(knowledge)}, history条数:{len(history)}, mode:{mode}")
        
        is_knowledge_empty = not knowledge or knowledge.strip() == ""

        if mode == "strict":
            if is_knowledge_empty:
                self.logger.info(f"strict模式下, 知识为空")
                sys_prompt = doc_qa_strict_empty_sys_prompt
                user_prompt = doc_qa_strict_empty_user_prompt
            else:
                self.logger.info(f"strict模式下, 知识不为空")
                sys_prompt = doc_qa_strict_nonempty_sys_prompt
                user_prompt = doc_qa_strict_nonempty_user_prompt
        else:  # common mode
            if is_knowledge_empty:
                self.logger.info(f"common模式下, 知识为空")
                sys_prompt = doc_qa_common_empty_sys_prompt
                user_prompt = doc_qa_common_empty_user_prompt
            else:
                self.logger.info(f"common模式下, 知识不为空")
                sys_prompt = doc_qa_common_nonempty_sys_prompt
                user_prompt = doc_qa_common_nonempty_user_prompt
        messages = [{"role": "system", "content": sys_prompt}]
        for item in history[::-1]:
            # 安全检查：确保item是字典类型且包含必要的键
            if isinstance(item, dict) and "query" in item and "content" in item:
                messages.append({"role": "user", "content": item["query"]})
                messages.append({"role": "assistant", "content": item["content"]})
            else:
                # 记录无效的历史记录项
                self.logger.warning(f"跳过无效的历史记录项: {item}")
        formatted_query = user_prompt.replace("{{query}}", query).replace("{{body}}", knowledge)
        messages.append({"role": "user", "content": formatted_query})
        return messages
    
    async def generate(
        self, 
        query: str, 
        user_id: str, 
        conversation_id: str,
        history: List[Dict] = None,
        timeout: Optional[float] = None,
        enable_thinking: bool = True,
        mode: str = 'common',
        **kwargs
    ) -> Dict[str, Any]:
        """
        非流式文档问答生成
        """
        if history is None:
            history = []
            
        # 检索文档
        knowledge = await self._retrieve_knowledge(query, user_id, conversation_id=conversation_id)
        self.logger.info(f"文档检索完成")
        
        # 构建消息
        messages = self._build_messages(query, history, knowledge, mode=mode)
        
        # 调用模型生成回答
        return await self.provider.generate(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            **kwargs
        )
    
    async def generate_stream(
        self, 
        query: str, 
        user_id: str, 
        conversation_id: str,
        history: List[Dict] = None,
        timeout: Optional[float] = None,
        enable_thinking: bool = False,
        mode: str = 'common',
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        流式RAG问答生成
        """
        if history is None:
            self.logger.info(f"历史对话为空，开始新对话")
            history = []
        self.logger.info(f"历史对话条数: {len(history)}")
        # 检索文档
        knowledge = await self._retrieve_knowledge(query, user_id, conversation_id=conversation_id)
        
        format_knowledge = self.format_knowledge(knowledge)
        # self.logger.info(f"检索到的知识：{format_knowledge}")
        self.logger.info(f"文档检索完成，文档长度：{len(format_knowledge)}")
        
        # yield {"type": "reference", "content": json.dumps(knowledge, ensure_ascii=False), "role": "", "finish_reason": ""}
        
        self.logger.info(f"文档检索结果格式化处理")
        # 构建消息
        messages = self._build_messages(query, history, format_knowledge, mode=mode)
        # print(f"构建的消息：{json.dumps(messages, ensure_ascii=False)}")
        self.logger.info(f"开始调用模型服务")

        # 模型服务的流式调用
        async for chunk in self.provider.generate_stream(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            **kwargs
        ):
            yield chunk