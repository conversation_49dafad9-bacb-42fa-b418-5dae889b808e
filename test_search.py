#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试搜索功能的脚本
测试新的集合选择功能
"""

import sys
import os
import asyncio
import json
from typing import List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
if os.path.exists(env_file):
    load_dotenv(env_file)
    print(f"已加载{env_type}环境配置: {env_file}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    if os.path.exists('.env'):
        load_dotenv('.env')
        print("已加载默认环境配置: .env")
    else:
        print(f"警告: 环境配置文件不存在: {env_file} 或 .env")

from pipelines.search import SEARCH
from config.all_search_config import get_collections_by_types, COLLECTION_MAPPING


async def test_search_all_collections():
    """测试搜索所有集合（默认行为）"""
    print("\n===== 测试搜索所有集合 =====")
    
    search_service = SEARCH(request_id="test-all-collections")
    query = "复合应力导致 FPC 断线案例"
    user_id = "test_user"
    
    print(f"查询: {query}")
    print(f"用户ID: {user_id}")
    print("集合: 所有集合（默认）")
    
    try:
        async for result in search_service.search_all_collections(
            query=query,
            user_id=user_id,
            top_k=5,
            top_r=3,
            min_score=0.3
        ):
            print(f"搜索结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
    except Exception as e:
        print(f"搜索失败: {str(e)}")


async def test_search_specific_collections():
    """测试搜索指定集合"""
    print("\n===== 测试搜索指定集合 =====")
    
    search_service = SEARCH(request_id="test-specific-collections")
    query = "汽车发动机故障诊断"
    user_id = "test_user"
    
    # 测试不同的集合组合
    test_cases = [
        ["car"],  # 只搜索汽车知识库
        ["hardware"],  # 只搜索硬工知识库
        ["data"],  # 只搜索数据库
        ["car", "hardware"],  # 搜索汽车和硬工知识库
        ["data", "hardware"],  # 搜索数据和硬工知识库
        ["car", "hardware", "data"],  # 搜索所有集合
    ]
    
    for collections in test_cases:
        print(f"\n--- 测试集合组合: {collections} ---")
        print(f"查询: {query}")
        print(f"用户ID: {user_id}")
        
        try:
            async for result in search_service.search_all_collections(
                query=query,
                user_id=user_id,
                top_k=3,
                top_r=2,
                min_score=0.3,
                collections=collections
            ):
                print(f"搜索结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
        except Exception as e:
            print(f"搜索失败: {str(e)}")


async def test_search_invalid_collections():
    """测试无效集合类型"""
    print("\n===== 测试无效集合类型 =====")
    
    search_service = SEARCH(request_id="test-invalid-collections")
    query = "测试查询"
    user_id = "test_user"
    
    # 测试无效的集合类型
    invalid_collections = ["invalid_type", "unknown_collection"]
    
    print(f"查询: {query}")
    print(f"用户ID: {user_id}")
    print(f"无效集合: {invalid_collections}")
    
    try:
        async for result in search_service.search_all_collections(
            query=query,
            user_id=user_id,
            top_k=3,
            collections=invalid_collections
        ):
            print(f"搜索结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
    except Exception as e:
        print(f"搜索失败: {str(e)}")


async def test_search_empty_collections():
    """测试空集合列表"""
    print("\n===== 测试空集合列表 =====")
    
    search_service = SEARCH(request_id="test-empty-collections")
    query = "测试查询"
    user_id = "test_user"
    
    print(f"查询: {query}")
    print(f"用户ID: {user_id}")
    print("集合: 空列表（应该回退到所有集合）")
    
    try:
        async for result in search_service.search_all_collections(
            query=query,
            user_id=user_id,
            top_k=3,
            collections=[]
        ):
            print(f"搜索结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
    except Exception as e:
        print(f"搜索失败: {str(e)}")


def test_collection_mapping():
    """测试集合映射功能"""
    print("\n===== 测试集合映射功能 =====")
    
    print("可用的集合映射:")
    for key, collections in COLLECTION_MAPPING.items():
        print(f"  {key}: {len(collections)} 个集合")
        for collection in collections:
            print(f"    - {collection.get('collection_name', 'Unknown')}")
    
    # 测试get_collections_by_types函数
    test_cases = [
        ["car"],
        ["hardware"],
        ["data"],
        ["car", "hardware"],
        ["invalid_type"],
        [],
        None
    ]
    
    print("\n测试get_collections_by_types函数:")
    for collections_input in test_cases:
        result = get_collections_by_types(collections_input)
        print(f"输入: {collections_input} -> 输出: {len(result)} 个集合")


async def main():
    """主函数"""
    print("搜索功能测试脚本")
    print("=" * 50)
    
    # 测试集合映射功能
    test_collection_mapping()
    
    # 测试搜索功能
    await test_search_all_collections()
    await test_search_specific_collections()
    await test_search_invalid_collections()
    await test_search_empty_collections()
    
    print("\n" + "=" * 50)
    print("所有测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
