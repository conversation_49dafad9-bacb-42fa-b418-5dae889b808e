[[{"url": "https://xiaomi.f.mioffice.cn/docx/doxk4hUeany2ujanpEYxTiKlC4b", "name": "日志", "fileType": "feishu-docx", "content": "# 日志\n\n## 20250802（周六）\n\n- [#DSTE](https://xiaomi.f.mioffice.cn/wiki/CH2jwqVgniuHAak7cmjkNDGN48c)\n\n  - [手机部战略研讨会](https://xiaomi.f.mioffice.cn/wiki/Q39lwxOXeiUCUokAvTKkQRPM4ge)\n- [#IPD 规划](https://xiaomi.f.mioffice.cn/wiki/G6k2wSK35iIrlgkcAuFk3uho4fg) 确认 BOM 方案\n\n  - 定义了单据应该如何融入\n  - 自研 BOM 的方案怎么定：因为是使用规则、物料、产品结构是相互关联的，所以作业界面采用产品结构拍平 > 使用规则 > 物料的方式\n\n## 20250801（周五）\n\n- [#IPD 规划](https://xiaomi.f.mioffice.cn/wiki/G6k2wSK35iIrlgkcAuFk3uho4fg) 和毛老师单聊\n\n  - 把问题库和数据标准，完美咬合\n- ODC 访谈\n\n  - 负责难度：中等偏下\n  - 工作压力：正常\n  - 基础素质：尚可，沟通表达\n  - 没有特别强烈的转正和晋升诉求\n- #研产供周会\n\n  - 3 个牵引的项目要盘清楚工作范围，阶段性有产出和汇报\n  - 严控需求，3/4，重点项目的需求清单，每双周发我确认\n  - 体验检查\n\n## 20250731（周四）\n\n- [#IPD 规划](https://xiaomi.f.mioffice.cn/wiki/G6k2wSK35iIrlgkcAuFk3uho4fg) 测试部访谈\n\n  - [产品数据组讨论-到 930 希望达到的目标](https://xiaomi.f.mioffice.cn/docx/doxk4SMl2moGyDPf63LHaGiusog)\n  - 💡 大数据量也是大知识量，导入知识库\n    - 测试用例 33 万\n    - 代码\n- #ISC 周会\n\n  - 💡 需求计划的周报，作为做“分析”的标的\n- [#ISC 控制塔](https://xiaomi.f.mioffice.cn/wiki/TxfGwNwDvih97lkVC13kNUpw42f) 讨论\n\n  - 场景驱动\n    - 驾驶舱：BI/预警，小兵总\n    - 问答：查数/分析，全员\n    - 模拟：面向一线提效\n      - 融入到计划日历\n- [2025 H1 全员会 - 研产供](https://xiaomi.f.mioffice.cn/wiki/Y0dpw8XuGibaDXkbBYPkO6br4Wf)\n\n  - 成本用起来 作为第三个专项；返利治理，多业务线，采购金额看板\n\n## 20250730（周三）\n\n- [25Q2 战略专项 / 组织任务书 QBR｜ 集团信息技术部](https://xiaomi.f.mioffice.cn/docx/doxk4KraCyejTEopNLAeREPpuLd) 九爷：\n\n  - 原来哪些流程没有的/管得很乱，梳理清楚了\n  - 做了效果是什么，减少了多少人，剩了多少人\n  - 信息化领导小组：先抽象一层，经营管理的问题是共通的\n    - 业务有什么挑战和问题\n    - 考核一号位数字化部分\n  - 顶层设计，业务一号位要出一半\n- #ISC 多业务线\n\n  - 交付：全 O、AVAP 有问题\n    - 可穿戴的 ODM 料是重灾区\n  - 成本：严重缺失\n  - 流程管理部\n    - 规划前置、规则明确；三年可视、一年可控\n  - 复盘\n    - 歌尔的全 OMRP 的使用情况\n    - 电视的双周需求不变\n- #研产供难点会：待办\n- [#IPD 规划](https://xiaomi.f.mioffice.cn/wiki/G6k2wSK35iIrlgkcAuFk3uho4fg)碰头会\n\n  - 未来一个月：P2、P3、Q200 的复盘引入业务问题\n  - 产品竞争力的指标\n    - 用户构建吸引、NPS、特性 NSS\n  - 谈了需求，就需要谈资源\n\n## 20250729（周二）\n\n- 准备 [25Q2 战略专项 / 组织任务书 QBR｜ 集团信息技术部](https://xiaomi.f.mioffice.cn/docx/doxk4KraCyejTEopNLAeREPpuLd)\n\n  - 一个计划控制工厂，供应商和仓库。 过去主做的是控制，今天用计划赋能\n  - 做的不好的用，空调\n- [#IPD 规划](https://xiaomi.f.mioffice.cn/wiki/G6k2wSK35iIrlgkcAuFk3uho4fg)：分享需求管理\n\n  - [整机产品开发流程 +P3 需求管理 讨论会议纪要](https://xiaomi.f.mioffice.cn/wiki/VoqlwA5CIiIrDAkf4o2kKIAi4Og)\n  - 💡 产品数据：可以共创下目标、落地策略，以及各自讲下产品数据的范围，痛点\n\n## 20250728（周一）\n\n- [2025 H1 全员会 - 研产供](https://xiaomi.f.mioffice.cn/wiki/Y0dpw8XuGibaDXkbBYPkO6br4Wf)\n  - 反思\n    到处撒胡椒面，越做越多，越做越散\n    系统性思考，抓主要矛盾\n\n认知不深，不能共识价值\n\n不能共识价值，不敢投入\n\n不敢投入，导致认知不深\n\n- [手机部例会重点内容](https://xiaomi.f.mioffice.cn/wiki/NXDcwBAjiijg9sk7qYpktbVb4Ic)周例会以及经分会\n\n---\n\n## 20250727（周日）\n\n- [#DSTE](https://xiaomi.f.mioffice.cn/wiki/CH2jwqVgniuHAak7cmjkNDGN48c)\n\n  - 💡 如果真的能够理解，一家大型公司的产品定义是怎么变革的，还是挺激动人心的\n- 下周重点\n\n  - IPD 的组织和里程碑要定下来\n  - ISC 目前仍不算清楚\n  - 明确四大专项 Q3 的目标：IPD 很清楚，AI 也算清楚，大家电（闭环），ISC 不清楚\n  - 绩效要处理下，oneone 完成\n  - 紧急问题：\n    - 处理多业务线的问题，以及数据治理的课题下·\n- Jeffry 的电话访谈\n\n  - 💡 产品化运作，我们现在是按照项目运作\n\n## 20250726（周六）\n\n[#AI 专项](https://xiaomi.f.mioffice.cn/wiki/INUewxNDXiZgEhkHdN5kmFO44ul)\n\n- [问题闭环思考](https://xiaomi.f.mioffice.cn/wiki/JgnOwseseicfrWkDttPkOA8d4dg)\n\n## 20250725（周五）\n\n- [#DSTE](https://xiaomi.f.mioffice.cn/wiki/CH2jwqVgniuHAak7cmjkNDGN48c)\n  - 三星：一个工厂，把整个团队打包出去，帮他看下情况\n  - ISC：大家电生态链 > 降本 > 智能运营\n  - IPD：端到端需求，已经包含了 前端的需求分析\n\n## 20250724（周四）\n\n- [#DSTE](https://xiaomi.f.mioffice.cn/wiki/CH2jwqVgniuHAak7cmjkNDGN48c) 务虚会\n\n  - ISC 2021 的变革目标：降 100 亿库存，成本和定价，交付稳定性\n  - 联系少，说不上话\n    - 本质是调研少，理解浅\n  - 越是没进展，越要汇报 [#方法论](https://xiaomi.f.mioffice.cn/wiki/VQoUwf70yi0X0tkW6e1kZdJ24Jc)\n- [#IPD 规划](https://xiaomi.f.mioffice.cn/wiki/G6k2wSK35iIrlgkcAuFk3uho4fg) 正式启动会\n\n  - 发言\n    - 数据是所有的信息化，以及变革的底层，背后。\n    - 从过去的经验， 信息化系统建设有周期，数据治理是长期的运营\n      因此：\n      - 首先，需要先**定义主数据**，围绕主数据构建数据流通链路。 [2025/6/3_研产供主数据梳理](https://xiaomi.f.mioffice.cn/sheets/shtk4XJrmwfdHGy4JDyp1hhHkxg?disposable_login_token=eyJwa2dfYnJhbmQiOiJmZWlzaHUiLCJ1bml0Ijoia2E0bGFyayIsInB3ZF9sZXNzX2xvZ2luX2F1dGgiOiIxIiwidmVyc2lvbiI6InYzIiwiZGV2aWNlX2xvZ2luX2lkIjoiNzIwNDY0MTI2ODA0NTk3MTU2NCIsInRpbWVzdGFtcCI6MTc0OTc3OTU4NSwidXNlcl9pZCI6IjY3ODk5MTA3Njk1NjA0NTMxMjEiLCJ0ZW5hbnRfYnJhbmQiOiJmZWlzaHUifQ==.5821b3fab6152c0f03d78e49a77cbda214af6953176ccbd64767c0a79a4ea218&from=ipad_browser)\n      - 其次，定义 IPD 体系中涉及的**数据闭环**\n      - 最后，基于以上数据闭环共识，**推进统一工作台****，**把 6 大关键角色拉进同一工作框架，形成日常工作协同。\n  - 九爷\n    - 这次不是信息化的项目，矩阵管理是逃不开的。\n    - 苹果和特斯拉的职能，单一产品；吉利，只适合成熟领域。矩阵\n    - 横向的线，还是做二传手为主，专业技术部门\n  - 卢总\n    - IPD 是小米最重要的流程，产品的流程做不好，其他都是假的\n    - 19 年实施在现在，主线是可以的，延伸了很多子流程，手机部有编程能力，来源不一\n    - 所造成的问题是，流程不统一、数据不统一\n      - TR 评审是线下评审，很难保证数据的真实性\n      - 能不能聚焦信息化\n    - 1 个多月让信息部整合了流程\n      - 在产品开发流程做完，商业成功\n      - 这么多流程、数据，拼接和打通，还是下定决心\n        - 下定决心就是做一套系统\n      - 即使信息化项目也不是\n    - 把我们这么多年的 67 系统，整合成一个大流程和大系统，形成一套数据，真实不受人为干预的。\n      - 把整个产品的开发\n      - 产品、技术、OS 进行耦合，OS 的规划\n      - OS3，整体的开发只给了 1.5 个月\n        - 为了 TR 过不去，规划太后置\n      - 项目管理的问题是散落，很难成为项目的守护者，很难\n        - 重要的是要把项目管理拉高，手机部的项目管理部门，人员的专业性\n      - 所有的流程、组织变革，最后是业务变革\n      - 信息化是\n      - ISC 铁三角\n\n## 20250723（周三）\n\n- [#AI 专项](https://xiaomi.f.mioffice.cn/wiki/INUewxNDXiZgEhkHdN5kmFO44ul) 灵感\n\n  - 虽然不做联网，但是可以联网高质量的书拿进来（直接去搜集红宝书，或者找高质量的文章）\n  - 知识问答\n    - 把 Agent 引进来，Agent 有更深入的知识\n    - 低质量问题 →  被抛出来 →  解决\n- [#DSTE](https://xiaomi.f.mioffice.cn/wiki/CH2jwqVgniuHAak7cmjkNDGN48c) 后续重要方向\n\n  - 战略上，ISC 就是做计划和按计划执行，控制塔解决计划质量问题，剩下的就是厂和商的管理和赋能\n    - 计划做准\n    - 先管住再赋能厂和商\n    - 生态链和大家电变革，搞清楚业务价值\n  - 概预核决\n  - IPD 需求管理，项目管理，TR 管理，DCP 管理，知识管理\n- [#ISC 控制塔](https://xiaomi.f.mioffice.cn/wiki/TxfGwNwDvih97lkVC13kNUpw42f) ISC 闭环\n\n  - 计划越做越好\n    - 预测和模拟越做准\n  - 知识让问答强大，什么让算法更强大\n\n---\n\n## 20250722（周二）\n\n- [#IPD 规划](https://xiaomi.f.mioffice.cn/wiki/G6k2wSK35iIrlgkcAuFk3uho4fg) 准备 PPT\n\n  - 找卢总关于数字化是抓手\n    - [新零售实践分享](https://xiaomi.f.mioffice.cn/docs/dock4uee59k0zbS1E1xLW8w0Oih)\n  - 数字化的问题\n    - **不存在「一个 IPD 系统」**，只有“67 个 IPD 相关系统”\n      **数据未循环，没有动力维护数据**\n    - 弱标准，弱协同，系统功能有有了流程和信息化耦合度不够\n- #研产供难点会\n\n  - 控制塔的要控制住这个方向：逆向推衍下（端到端有控制哪些？）\n    - 计划、厂、仓、商\n    - 计划：从只控制备拉配的计划，延伸到主计划、MDS、MPS 等\n- #硬工 QBR\n\n  - 支持 26 年的预算\n\n## 20250721（周一）\n\n- SAP 团队合并 陈阳\n  - 2 月份和高明已经有沟通\n    - 架构\n      - 没有用到 SAP 里面的功能，稳定性和效率没有释放出来，在架构方面有缺失\n    - 工作模式有非常大的问题（产品方案已经做到 FS，没有任何技术把关）\n      - 第三周、第四周，只要评审过的，都被打回去。但是马上要升级、归档。\n      - 技术委的单测要求，没有任何实际用途\n    - 解法\n      - 只能 Push 研发往前站，顺应标准程序的行为\n  - 你理解今天面临的挑战？短期目标？\n    --------------------------------\n  - 收益\n    ----\n  - 关键负责人：高明\n"}], null]