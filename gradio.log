2025-07-31 22:08:48.803 | INFO     | __main__:<module>:20 - 已加载环境配置: .env.local
2025-07-31 22:08:50.042 | INFO     | frontend.gradio_app_v2:<module>:20 - 日志初始化完成，日志路径: /Users/<USER>/work/04_dms/idp_agent/log/frontend.log
2025-07-31 22:08:50.043 | INFO     | frontend.gradio_app_v2:<module>:32 - 成功导入前端配置文件
2025-07-31 22:08:50.044 | INFO     | frontend.core.chat_app:<module>:19 - 成功导入前端配置文件
2025-07-31 22:08:50.045 | INFO     | frontend.api.client:<module>:21 - 成功导入前端配置文件
2025-07-31 22:08:50.047 | INFO     | frontend.ui.components:<module>:21 - 成功导入前端配置文件
启动Gradio前端应用...
服务器地址: 0.0.0.0:7863
公共链接: 否
调试模式: 是
2025-07-31 22:08:50.049 | INFO     | __main__:main:68 - 启动Gradio前端应用，地址: 0.0.0.0:7863，公共链接: 否，调试: 是
2025-07-31 22:08:50.049 | INFO     | frontend.gradio_app_v2:create_gradio_interface:59 - 开始创建Gradio界面
2025-07-31 22:08:50.049 | INFO     | frontend.core.chat_app:__init__:41 - ChatApp初始化，API地址: http://localhost:8080/api/v1
2025-07-31 22:08:50.049 | INFO     | frontend.api.client:__init__:42 - APIClient初始化，API地址: http://localhost:8080/api/v1
2025-07-31 22:08:50.085 | INFO     | frontend.gradio_app_v2:create_gradio_interface:129 - Gradio界面创建完成
Running on local URL:  http://0.0.0.0:7863

To create a public link, set `share=True` in `launch()`.
2025-07-31 22:09:35.420 | INFO     | frontend.handlers.chat_handlers:chat_stream:155 - RAG chat_stream被调用
2025-07-31 22:09:35.420 | INFO     | frontend.handlers.chat_handlers:chat_stream:176 - [RAG][请求开始] request_id=771c28de-9dba-4e82-bee8-f7cbefb92503, user_id=user123, model_id=qwen3_32b, query=点胶设计规范, top_k=20
2025-07-31 22:10:21.118 | INFO     | frontend.handlers.chat_handlers:chat_stream:67 - LLM chat_stream被调用
2025-07-31 22:10:21.120 | INFO     | frontend.handlers.chat_handlers:chat_stream:87 - [LLM][请求开始] request_id=20e040ad-b610-4369-b3d1-f43afa628325, user_id=user123, model_id=qwen3_32b, query=你好
2025-07-31 22:10:24.258 | INFO     | frontend.handlers.chat_handlers:chat_stream:231 - [RAG][请求结束] request_id=771c28de-9dba-4e82-bee8-f7cbefb92503, user_id=user123, model_id=qwen3_32b, query=点胶设计规范, content=

点胶设计规范涉及多个关键设计要求，以下是核心要点总结：  

### 1. **灌胶孔设计规范**  
- **灌胶孔直径**：常规项目要求灌胶孔直径≥1.70mm <ref refNum="[1
2025-07-31 22:10:30.400 | INFO     | frontend.handlers.chat_handlers:chat_stream:327 - CAR chat_stream被调用
2025-07-31 22:10:30.401 | INFO     | frontend.handlers.chat_handlers:chat_stream:346 - [CAR][请求开始] request_id=7fe4ee97-8779-46e1-94cc-ef06ff83d94b, user_id=user123, model_id=qwen3_32b, query=rip 世界, top_k=20
2025-07-31 22:10:33.596 | INFO     | frontend.handlers.chat_handlers:chat_stream:131 - [LLM][请求结束] request_id=20e040ad-b610-4369-b3d1-f43afa628325, user_id=user123, model_id=qwen3_32b, query=你好, content=

您好！我是您的硬件研发领域AI助手，专注于为工程师提供专业支持。  
请问您需要咨询以下哪方面内容？  
- **设计规范**（Checklist/布局/叠层等）  
- **失效案例分析**（历
2025-07-31 22:10:39.442 | INFO     | frontend.handlers.chat_handlers:chat_stream:398 - [CAR][请求结束] request_id=7fe4ee97-8779-46e1-94cc-ef06ff83d94b, user_id=user123, model_id=qwen3_32b, query=rip 世界, content=

未检索到与"rip 世界"相关的有效信息。根据现有知识库内容，FANUC报警代码中涉及的RIPE系列代码（如RIPE-009/RIPE-010）仅与机器人信息协议相关，但文档中未提及任何关于"ri
/Users/<USER>/.local/share/uv/python/cpython-3.9.21-macos-aarch64-none/lib/python3.9/multiprocessing/resource_tracker.py:216: UserWarning: resource_tracker: There appear to be 8 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
