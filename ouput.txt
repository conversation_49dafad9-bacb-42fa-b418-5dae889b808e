


参考frontend文件夹中的tmp.py，构建一个前端页面，要求如下：
1、 页面上要实现LLM问答API、RAG问答API、ATAQA问答API三个接口的调用。
2、前端页面只用支持流式输出，且思考过程和正式回复过程要在不同的文本框里。
3、RAG问答API、ATAQA问答API流式调用时，第一个返回的流式内容为知识库召回的参考内容，需要在页面左边进行显示。LLM问答API调用不会返回知识库召回内容，直接显示思考过程和回答内容。
4、有多轮对话功能，当用户输入新问题时，需要把该用户在当前页面的历史问答也返回给api接口。
5、页面上需要有清空历史会话功能，清空历史对话后，再发送问题时，传给api接口的历史对话为一个空列表。


使用gradio构建一个前端页面，要求如下：
1、实现API接口调用
- LLM问答API、RAG问答API、ATAQA问答API 三个接口分别实现。
- 支持流式输出
2、流式输出与分区显示
- 页面两个分区，最左侧为参数配置区域，右侧为对话区域。
- RAG/ATAQA 接口首次流内容为知识库参考，显示在对话区域的左半部分。思考过程和正式回复分别显示在不同文本框，均在页面右侧，其中思考过程在正式回复上方
- LLM问答接口，展示在对话区域，思考过程和正式回复分别显示在不同文本框，其中思考过程在正式回复上方
3、多轮对话
- 记录历史问答，每次提问时带上历史对话发给API。
- 历史对话区展示所有轮次。
4、清空历史会话
- 点击按钮，清空历史对话记录，下次提问时，传给API的历史对话为一个空列表。
5、api调用方式请参考test_api.py
LLM问答API (/api/v1/llm-qa)
RAG问答API (/api/v1/rag-qa)
DATAQA问答API (/api/v1/data-qa)
6、api接口返回的内容格式如下，其中LLM问答API，type字段没有reference。

| 字段名          | 可能值                                                                 | 描述                                                                                                                                 |
|----------------|-----------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------|
| conversation_id | 字符串                                                                | Ω会话Id                                                                                                                              |
| msg_id         | 字符串                                                                | 消息Id                                                                                                                               |
| type           | ● reference<br>● content 表示模型的正式回答部分<br>● reasoning 表示模型的内部思考或推理过程 | 输出的内容类型                                                                                                                       |
| content        | 文本片段或空字符串                                                    | 模型输出的文本内容。结合 type 字段使用：<br>- 当 type 为 reasoning 时：思考推理内容<br>- 当 type 为 content 时：正式回答内容<br>- 当 type 为 reference 时：引用内容 |
| role           | ● assistant                                                           | 对话中发言者的角色，用于区分上下文，首次出现时非空                                                                                    |
| finish_reason  | ● ""：输出尚未结束，后续还有数据<br>● stop：输出正常停止（例如，模型完成回答）<br>● length：达到 max_tokens 限制 | 输出结束的原因。None 表示输出仍在继续；非 None 值表示输出已完成                                                                        |
7、api接口返回的内容格式如下：

data: {"type": "reference", "content": "[{\"title\": \"FPC板厂工艺时间表\", \"content\": \"FPC 点击微动失效原因汇总", \"docName\": \"FPC板厂工艺时间表\", \"docUrl\": \"https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g\", \"sheetName\": \"\"}, {\"title\": \"FPC板厂工艺时间表\", \"content\": \"原因分析\\nFPC 断线位置\\n**核心结论：断线位置集中转轴盖对应位置，****FPC**** 中间区域出现裂纹，然后往两边延伸，内层存在裂纹；\", \"docName\": \"FPC板厂工艺时间表\", \"docUrl\": \"https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g\", \"sheetName\": \"FPC 断线位置\"}]", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
data: {"type": "content", "content": "", "role": "assistant", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
data: {"type": "reasoning", "content": "\n", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
data: {"type": "reasoning", "content": "好的", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
...
data: {"type": "reasoning", "content": "单独", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
data: {"type": "content", "content": "对于", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
...
data: {"type": "content", "content": "", "role": "", "finish_reason": "stop", "msg_id": "UUID", "conversation_id": "UUID"}



本地使用test_api.py测试，日志结果如下：
2025-07-09 11:58:10.696 | INFO | api.app:startup_event:43 | 应用程序启动
INFO:     Application startup complete.
INFO:     127.0.0.1:60618 - "GET /api/v1/health HTTP/1.1" 200 OK
2025-07-09 11:58:13.961 | INFO | request_id=5381e6c8-6d01-49db-8a6f-7da23ab421fc | api.routes:llm_qa:76 | LLM问答请求开始: 库存周转率如何计算？
2025-07-09 11:58:13.983 | INFO | request_id=5381e6c8-6d01-49db-8a6f-7da23ab421fc | api.routes:llm_qa:79 | 历史记录条数: 2
INFO:     127.0.0.1:60620 - "POST /api/v1/llm-qa HTTP/1.1" 200 OK
enable_thinking: False
2025-07-09 11:58:13.985 | INFO | request_id=5381e6c8-6d01-49db-8a6f-7da23ab421fc | api.routes:generate_stream:83 | 开始生成流式响应
2025-07-09 11:58:13.986 | INFO | request_id=5381e6c8-6d01-49db-8a6f-7da23ab421fc | pipelines.llm_qa:_build_messages:36 | 构建消息列表：[{'role': 'system', 'content': '\n你是一个供应链领域知识专家，请根据用户的问题进行回复。\n'}, {'role': 'user', 'content': '什么是供应链管理？'}, {'role': 'assistant', 'content': '供应链管理是指对供应链中的信息流、物流和资金流进行计划、组织、协调与控制的过程。'}]
chunk: ChatCompletionChunk(id='chatcmpl-4b48955cecd34fb3b0bf0597814fb6c3', choices=[Choice(delta=ChoiceDelta(content='', function_call=None, refusal=None, role='assistant', tool_calls=None), finish_reason=None, index=0, logprobs=None)], created=1752033494, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
chunk: ChatCompletionChunk(id='chatcmpl-4b48955cecd34fb3b0bf0597814fb6c3', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, refusal=None, role=None, tool_calls=None, reasoning_content='\n'), finish_reason=None, index=0, logprobs=None)], created=1752033494, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
chunk: ChatCompletionChunk(id='chatcmpl-4b48955cecd34fb3b0bf0597814fb6c3', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, refusal=None, role=None, tool_calls=None, reasoning_content='好的'), finish_reason=None, index=0, logprobs=None)], created=1752033494, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
chunk: ChatCompletionChunk(id='chatcmpl-4b48955cecd34fb3b0bf0597814fb6c3', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, refusal=None, role=None, tool_calls=None, reasoning_content='，'), finish_reason=None, index=0, logprobs=None)], created=1752033494, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
...
2025-07-09 11:59:26.316 | INFO | request_id=2ff49141-48fc-4bca-b7e0-1606e48c2671 | api.routes:generate_stream:98 | LLM流式问答完成，耗时: 20.83秒
前端页面测试，日志如下：
2025-07-09 11:57:03.119 | INFO | request_id=57e4189e-14cf-48c8-a776-d45482bbb800 | api.routes:llm_qa:76 | LLM问答请求开始: 你好
2025-07-09 11:57:03.175 | INFO | request_id=57e4189e-14cf-48c8-a776-d45482bbb800 | api.routes:llm_qa:79 | 历史记录条数: 0
INFO:     127.0.0.1:57807 - "POST /api/v1/llm-qa HTTP/1.1" 200 OK
2025-07-09 11:57:03.180 | INFO | request_id=57e4189e-14cf-48c8-a776-d45482bbb800 | api.routes:generate_stream:83 | 开始生成流式响应
enable_thinking: False
2025-07-09 11:57:03.181 | INFO | request_id=57e4189e-14cf-48c8-a776-d45482bbb800 | pipelines.llm_qa:_build_messages:36 | 构建消息列表：[{'role': 'system', 'content': '\n你是一个供应链领域知识专家，请根据用户的问题进行回复。\n'}]
chunk: ChatCompletionChunk(id='chatcmpl-ad8f69e8c906423d979c42fc4489fed1', choices=[Choice(delta=ChoiceDelta(content='', function_call=None, refusal=None, role='assistant', tool_calls=None), finish_reason=None, index=0, logprobs=None)], created=1752033423, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
chunk: ChatCompletionChunk(id='chatcmpl-ad8f69e8c906423d979c42fc4489fed1', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, refusal=None, role=None, tool_calls=None, reasoning_content='\n'), finish_reason=None, index=0, logprobs=None)], created=1752033423, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)

前端服务的日志：
INFO:httpx:HTTP Request: POST http://localhost:8080/api/v1/llm-qa "HTTP/1.1 200 OK"
INFO:api_client:开始接收流式响应: llm-qa
前端收到chunk: type=content, content='', finish_reason=''
前端收到chunk: type=reasoning, content='
', finish_reason=''

请分析，为什么使用test_api.py测试LLM问答api时，api服务的日志都正常。而在前端页面进行测试时，LLM问答API的日志显示，输出两个以后就不再输出了。



接口调整：
1、llm问答接口开放temperature等参数调整
2、硬工知识库问答接口开放top_k等参数调整
3、R平台问答接口开放



前端修改建议：
1、参考知识召回、思考过程、回复内容、增加耗时记录
2、三个api接口对应不同的服务，对话不能串用
3、思考过程框、回复内容框、知识库参考都要固定大小
4、问题库AI移到右侧
5、左侧配置拦进一步缩小，左侧只有配置有关内容，右侧整体区域增大
6、发送按钮移动到输入问题框右侧，清空历史按钮移到发送按钮右侧


前端修改建议：
1、耗时应该分开统计，放在对应label的右边，如获得知识库参考的耗时、思考过程的耗时、回复内容的耗时
2、知识库参考、思考过程、回复内容、对话历史这几个框里的内容都需要是支持markdown格式渲染
3、整体页面放大，缩小配置区域，放大知识库参考、思考过程和回复内容框
4、发送按钮调整较小
5、左侧配置显示如下信息：
  -对话配置：用户id、对话id
 - 模型配置
 - 检索配置： 检索数量上限调整为20
 - 对话历史清空按钮

  
请按照如下要求调整前端页面：
1、页面应该自适应浏览器窗口大小，铺满整个浏览器，放大知识库参考、思考过程和回复内容框、发送按钮调整较小
2、知识库参考、思考过程、回复内容、对话历史这几个框里的内容都需要是支持markdown格式渲染，不同界面组件应该有明确的边界。
3、选择不同api接口时，页面布局应该初始化好。
4、知识库参考、思考过程，回复内容需要有label
5、耗时应该分开统计，放在对应label的右边，如获得知识库参考的耗时、思考过程的耗时、回复内容的耗时，耗时放在label的右边，紧挨着label
6、把问题库AI大标题移到最左侧
7、左侧共有如下信息：
 - 大标题
  -对话配置：用户id、对话id
 - 模型配置
 - 检索配置： 检索数量上限调整为20
 - 对话历史清空按钮
8、页面设计尽可能简洁美观

请按照如下建议继续修改前端页面：
1、思考过程、回复内容、知识库参考框都可以放到最大，有复制选项
2、当浏览器窗口较小时，页面不支持上下滑动，导致看不到底部的信息
3、当浏览器窗口较大时，依然没有做到自适应窗口大小，而是都是集中到页面左侧。
4、耗时没有正常显示，应该将获得知识库参考的耗时、思考过程的耗时、回复内容的耗时，耗时放在每个模块label的右边，紧挨着label，耗时单位为秒，保留一位小数。
5、清空历史对话按钮报错


如图所示，前端页面如下几个问题：
1、当浏览器窗口较大时，依然没有做到自适应窗口大小，而是都是集中到页面左侧。
2、知识库参考、思考过程和回复内容，几个label右侧的时间一直停留在0.0秒，没有正常显示耗时。知识库参考计时规则为从发送query，到获得知识库参考结束。思考过程计时规则为从获得知识库参考结束，到获得思考过程结束。回复内容计时规则为从获得思考过程结束，到获得回复内容结束。
3、知识库参考、思考过程和回复内容、 对话历史几个框没有放到最大选项，复制选项被框边缘遮挡。
4、知识库参考、思考过程和回复内容、 对话历史几个label应该紧贴下面的内容显示框，因为参考知识和模型回复内容较多，所以需要要紧凑布局，尽可能用多的地方展示这些信息。
5、知识库参考和数据参考里的具体内容，字体小一些。思考过程的字体也要小一些。
6、数据参考与知识库参考一样的设计。
7、在页面左侧增加首token响应时间，为从发送问题，到收到api第一个chunk的时间。


请按照如下建议修改前端界面：
1、以下几个label和对应的value显示上应该在同一行：用户id、对话id、首token响应时间、模型选择
2、知识库参考、思考过程、回复内容这几个标签后面的耗时依然未正常显示，一直显示时0.0。知识库参考计时规则为从发送query，到获得知识库参考结束。思考过程计时规则为从获得知识库参考结束，到获得思考过程结束。回复内容计时规则为从获得思考过程结束，到获得回复内容结束。
3、show_copy_button复制按钮被边缘圆角边框给遮挡了。
4、思考过程、知识库参考、对话历史、数据参考这几个textbox要有最大高度限制，不能随着内容的增多而一只调大高度。回复内容textbox需要根据输出内容的长度，动态调整高度。



请按照如下要求修改前端代码：
1、新增汽车知识库问答模块，对应的 api接口为 rag-qa 中的knowledge_type == "car"时的分支。
2、新增 all问答模块，对应的 api接口为 all-qa。
3、all问答模块和汽车知识库问答模块，请参考硬工知识库问答模块的实现方式。
4、新增一个检索模块，对应的 api接口为 search，对应的页面只需要显示输入问题和检索结果即可。


按照如下要求修改前端代码：
1、前端代码都在一个文件中，可维护性和可扩展性较差，请拆分多个文件，每个文件对应一个功能模块。增强代码的可扩展性。
2、不要调整任何页面的样式，和功能性的东西，只对代码进行拆解，拆解以后代码复用性应该会更好。
3、对应的修改启动脚本 run_gradio.py


请按照如下要求修改前端代码，
1、问题库AI 标题应该移到左侧配置栏。
2、调整硬工知识库问答、R 平台问答、汽车知识库、全库问答的布局。知识库参考应该在问题栏下面左侧的位置，思考过程和回复应该在知识库参考的右侧位置。
3、思考过程、知识库参考、对话历史、数据参考这几个textbox要有最大高度限制，不能随着内容的增多而一只调大高度。回复内容textbox需要根据输出内容的长度，动态调整高度。
4、思考过程框支持折叠。


按照如下要求继续修改代码：
1、页面布局调整不正确，知识库参考在问题栏下面左侧，思考过程和回复在知识库参考的下面。请把思考过程和回复内容调整到知识库参考的右侧。
2、去除知识库参考，思考过程、知识库参考、数据参考label旁边的记时显示。
3、页面采用紧凑布局，字体都调小。
4、首 token响应没有生效，首 token时间为从发送问题，到收到api第一个chunk的时间。


请认真分析该代码工程，输出分析文档：
1、分析该代码工程可以提升的方面；
2、分析该代码工程存在的问题和 bug；
3、给出实际的优化建议。
5、分析在功能层面，性能和稳定性是否有提升空间；
6、代码规范是否达标；
7、不要直接修改代码，只给出非常详细的建议。



请按照如下要求修改前端代码：
1、知识库参考、回复内容、对话历史等标签参考思考过程标签的实现，不要再用 gr.HTML
2、硬工知识库、R 平台问答、汽车知识库、全库问答的布局一样，知识库参考位于输入问题框的下面左侧，思考过程和回复内容位于输入问题框的下面的右侧。
3、切换不同的模式时，页面最左侧的配置栏需要动态的改变，基于对应的 api 接口，动态的生成配置栏。配置栏和当前模式匹配。



当前的布局是按照如下设计思路实现的：

知识库参考内容在左侧（输入框下方的左侧）
思考过程和回复内容在右侧（输入框下方的右侧）
思考过程和回复内容垂直排列在右侧区域内

但是实际生成的前端页面，知识库参考位于输入问题框的下面左侧，而思考过程和回复内容位于知识库参考的下面，不是右边。这是什么原因造成的？请认真分析，并解决这个问题。


请按照如下要求修改前端代码：
1、左侧配置栏的宽度调窄，配置栏里的内容与对应的模式匹配，清空历史对话按钮需在左侧配置栏的底部，只有一个，表示清空当前模式的历史对话。
2、左侧配置栏支持折叠到最左侧，点击折叠按钮，折叠到最左侧。
3、左侧配置栏首token响应时间移到最上面
4、针对除检索外的其他模式，配置栏增加一个是否开启深度思考按钮。
5、左侧配置栏采取紧凑布局风格。


请按照如下要求修改代码：
1、硬工知识库、R 平台问答、汽车知识库、全库问答这几个模式下的左侧配置栏的清空历史对话按钮没有正常显示，只有 LLM 问答模式下的清空历史对话按钮正常显示了。
2、硬工知识库、R 平台问答、汽车知识库、全库问答这几个模式下，左侧配置栏缺少检索配置选项。
3、LLM问答模式下，左侧配置栏缺少问答模式选项。
4、检索模式下，左侧配置栏不需要有模型配置、和模式选择这些配置选项。
5、配置栏的折叠按钮点击后没有作用。
6、深度思考开关没有起作用，该开关应该和 api接口的enable_thinking参数绑定。
7、页面字体太小了，调大一些。


请按照如下要求修改代码：
1、页面左侧配置栏显示不正常，目前只有首token响应时间、用户ID、对话ID、模型选择这几个配置显示正常。
2、深度思考应该和 api接口的enable_thinking参数绑定。
3、配置栏的折叠按钮点击后没有作用。
4、选择不同的功能模式时，应该动态切换切换左侧的配置栏，不同功能模式的配置栏信息如下：
LLM问答 (index=0)：显示LLM配置（包括问答模式、深度思考、温度、Top-p）
硬工知识库 (index=1)：显示RAG配置（包括问答模式、深度思考、深度思考、检索数量、重排数量、最小相似度）
R平台问答 (index=2)：显示RAG配置（包括问答模式、深度思考、检索数量、重排数量、最小相似度）
汽车知识库 (index=3)：显示RAG配置（包括问答模式、深度思考、检索数量、重排数量、最小相似度）
全库问答 (index=4)：显示RAG配置（包括问答模式、深度思考、检索数量、重排数量、最小相似度）
检索 (index=5)：显示RAG配置（只有检索数量、重排数量、最小相似度）


请按照如下要求修改前端代码：
1、LLM问答的用户 id 和对话 id 不能正常输入，模型选择不能下拉选择。
2、硬工知识库、R平台问答、汽车知识库和全库问答的对话 id 不能正常输入。
3、检索功能对应的配置栏，重排数量和最小相似度为灰色，不能滑动调整。
4、请把问答模型下拉框改成选择框，默认选中，对应开启严谨模式。

请按照如下要求修改前端代码：
1、左侧配置栏内容较多，当浏览器窗口较小时，配置栏内容会变成两列，右侧那一列显示不出来，请调整配置栏的布局。
2、问答模式的严谨选项应该对应 api接口的 mode 为 strict 时，通用为 common，通用和严谨的选项分布在同一行。
3、LLM问答模式下，不要加问答模式的选项。
4、检索模式下，重排数量和最小相似度没有起效，且 api接口报错，检索接口的输出格式如下，请进行调整。
检索接口输出结果：
data: [{"collection": "hardwareKnowledge", "refs": []}, {"collection": "rDataQuery", "refs": []}]
报错：
2025-08-05 15:06:25.047 | ERROR    | frontend.handlers.chat_handlers:search_stream:558 - [SEARCH][异常] request_id=0a4d0ac4-b8c2-4393-abe3-8bf8bfb02bab, user_id=user_id, model_id=, query=点胶设计规范, error='list' object has no attribute 'get'

请按照如下要求修改代码：
1、点击左侧配置栏底部的清空当前模式历史，实际清空了所有历史，而不仅是当前模式历史，请修改代码，只清空当前模式的历史对话。
2、问答模式的两个选项：通用和严谨不在同一行，请务必确保两个选项在同一行。
3、左侧配置栏的折叠按钮点击后没有任何作用，请确保折叠按钮有效。
4、检索接口的输出结果如下，请根据输出结果进行格式化调整后，将结果展示在检索结果区域。
[{'collection': 'hardwareKnowledge', 'refs': [{'title': '点胶工艺防护设计规范', 'content': '点胶工艺防护设计规范\n二、范围\n【内容】\n本标准适用于小米公司自研与 ODM 非金属边框的手机。', 'docName': '点胶工艺防护设计规范', 'docUrl': 'https://xiaomi.f.mioffice.cn/wiki/wikk4u0mnUaIYCPmgZtTGJ47vd3', 'sheetName': '', 'owner': '', 'update_time': '2025-01-14 10:44:17', 'publish_time': '2024-09-26 17:02:06', 'doc_type': 'doc'}, {'title': '1.9.2.2.3 手机整机组装可制造性设计指导检查表DFM规范 V1.0', 'content': '', 'docName': '1.9.2.2.3 手机整机组装可制造性设计指导检查表DFM规范 V1.0', 'docUrl': 'https://xiaomi.f.mioffice.cn/wiki/IuTQwNrmRi2j9xkZCmukqL1h4qh', 'sheetName': '', 'owner': '', 'update_time': '2025-06-30 20:41:03', 'publish_time': '2025-07-04 09:29:26', 'doc_type': 'sheet'}]},{'collection': 'rDataQuery', 'refs': [{'title': '', 'content': '【内容】\n{"项目编码": "5轴点胶模块", "团队角色ID": "MEMBERS", "成员类型": "CHAT", "成员编码": "oc_a0433b69f4abe7ff1a950dded47d11a9", "删除标识": "0"}', 'docName': '', 'docUrl': 'https://ipd.mioffice.cn/miplm/i-table/project/plm_product_process', 'sheetName': '', 'owner': '', 'update_time': '', 'publish_time': '', 'doc_type': '团队成员'}, {'title': '', 'content': '【内容】\n{"项目编码": "5轴点胶模块", "团队角色ID": "PRODUCT MANAGER", "成员类型": "USER", "成员编码": "hujiaqi3", "删除标识": "0"}', 'docName': '', 'docUrl': 'https://ipd.mioffice.cn/miplm/i-table/project/plm_product_process', 'sheetName': '', 'owner': '', 'update_time': '', 'publish_time': '', 'doc_type': '团队成员'}, {'title': '', 'content': '【内容】\n{"项目编码": "5轴点胶模块", "团队角色ID": "PROJECT MANAGER", "成员类型": "USER", "成员编码": "zhangdi17", "删除标识": "0"}', 'docName': '', 'docUrl': 'https://ipd.mioffice.cn/miplm/i-table/project/plm_product_process', 'sheetName': '', 'owner': '', 'update_time': '', 'publish_time': '', 'doc_type': '团队成员'}, {'title': '', 'content': '【内容】\n{"MPNID编码": "3310000080C7", "MPNID描述": "三合一点胶水-黑色-7035B", "PN编码": null, "项目编码": null, "制造商简称": "Longcheer", "物料类型": "C", "物料组编码": null, "物料组名称": "", "应用项目": [], "大类名称": "", "中类名称": null, "小类名称": null, "业务线编码": ["MIPHONE"], "业务线名称": "手机（手机&平板&可穿戴）", "品牌": null, "选型状态": "", "采购人员": null, "采购层级编码": "NA", "采购层级描述": "NA", "业务状态编码": null, "业务状态描述": null, "采购方式编码": "", "采购方式描述": null, "业务线": "手机", "文档主ID": "", "规格书.文档ID": "", "规格书.容器ID": "", "规格书.规格书名称": "", "规格书.规格书链接": ""}', 'docName': '', 'docUrl': 'https://ipd.mioffice.cn/miplm/i-table/device-library/ipd_pdm_device_LM0m875s', 'sheetName': '', 'owner': '', 'update_time': '', 'publish_time': '', 'doc_type': '器件(MPNID编码)'}]}]

请按照如下要求修改前端代码：
1、请将首token响应时间输入框、用户 id 输入框、对话 id输入框、问答模型选择框、深度思考选择框都设定成固定宽度。
2、当问答完成时，请清空输入框中的内容。
3、点击清空当前模式历史时，虽然前端页面清空了对话历史，但实际问答时，对话历史未被清空。
4、请调整左侧配置栏开启深度思考的展示方式，使其更美观。
5、首 token响应时间没有响应，请检查代码，首token为发送请求后，到收到响应的时间间隔。


请按照如下要求修改前端代码：
1、点击清空当前模式历史时，前端页面所有模式的对话历史都被清空了，请修复。
2、首 token响应时间没有响应，请检查代码，首token为发送请求后，到收到响应的时间间隔。
3、左侧配置栏深度思考按钮点击没任何反应，也没起到什么作用，请修复。
4、左侧配置栏 问题库 AI 标题应该加粗放大展示，且在配置栏居中，请修复。

请按照如下要求修改前端代码：
1、首 token响应时间一直在闪动，请修复。
2、点击开启深度思考按钮时，现有展示方式，无法判断是否成功开启深度思考，请修复。
3、此外，开始深度思考按钮占的高度太大了，请修复。


请按照如下要求修改前端代码：
1、选中检索模式时，首 token响应时间应该显示为检索时间，为发送请求到收到结果的时间。
2、前端代码需要改为可以通过 reload 加载的方式。
3、深度思考按钮前面点击后闪动了一下，然后就恢复原样，依然没有展示当前情况是否开启了深度思考，请修复。



请按照如下要求修改前端代码：
1、前端启动脚本改成可以传递 reload重载参数。
2、开启深度思考按钮调整为勾选框的方式实现。
3、左侧配置栏底色应该都改为浅灰色。


请按照如下要求修改代码：
1、修改 search 类的实现，增加search_all_collections的入参，使得可以指定搜索的集合，参数为collection，为一个列表，如"["car", "hardware"]”,表示需要在CAR_SEARCH_COLLECTIONS和HARDWARE_SEARCH_COLLECTIONS中检索。如"["data"]”,表示需要在DATA_SEARCH_COLLECTIONS中检索。
2、对应的修改配置文件：all_search_config.py
3、修改  search 对应的 api 接口，使得可以指定检索的集合。
4、补充两个测试脚本：test_search.py和 test_search_api.py。
5、对应的修改前端页面中的检索模式，需要在检索模式时，最左侧可以选择要检索的集合。


我将对该代码工程进行全面分析。首先让我了解项目的整体结构和代码内容。

基于对该代码工程的全面分析，我将输出详细的分析文档：

# IDP Agent 代码工程分析报告

## 1. 项目概述

IDP Agent 是一个基于大语言模型的智能问答助手系统，采用 FastAPI + Gradio 架构，支持多种问答模式（LLM、RAG、数据问答、汽车知识库、全库问答）和知识库检索功能。

## 2. 可提升的方面

### 2.1 架构设计优化
- **微服务拆分**：当前所有功能集中在单一应用中，建议按业务域拆分为独立的微服务
- **缓存策略**：虽然有 Redis 缓存配置，但缺乏分层缓存和缓存失效策略
- **负载均衡**：缺乏多实例部署和负载均衡机制
- **消息队列**：对于耗时的检索和生成任务，建议引入异步消息队列

### 2.2 性能优化空间
- **连接池管理**：HTTP 客户端连接池配置可以进一步优化
- **并发控制**：缺乏请求限流和并发控制机制
- **数据库优化**：Redis 使用可以引入集群模式和读写分离
- **内存管理**：大量的知识检索结果可能导致内存占用过高

### 2.3 监控和可观测性
- **指标收集**：缺乏详细的业务指标和性能指标收集
- **链路追踪**：虽然有 OpenTelemetry 配置但被注释掉了
- **健康检查**：健康检查功能较为简单，缺乏依赖服务检查
- **告警机制**：缺乏主动告警和异常通知机制

## 3. 存在的问题和 Bug

### 3.1 安全问题
- **CORS 配置过于宽松**：`allow_origins=["*"]` 存在安全风险
- **API Token 硬编码风险**：Token 验证逻辑可能存在泄露风险
- **输入验证不足**：缺乏对用户输入的充分验证和清理
- **敏感信息泄露**：日志中可能包含敏感信息

### 3.2 代码质量问题
- **代码重复**：多个 Pipeline 类中存在大量重复的检索和重排逻辑
- **硬编码问题**：配置文件中存在硬编码的 URL 和参数
- **异常处理不一致**：不同模块的异常处理方式不统一
- **资源泄露风险**：HTTP 连接和 Redis 连接可能存在未正确关闭的情况

### 3.3 依赖管理问题
- **版本固定不当**：部分依赖使用 `>=` 可能导致兼容性问题
- **依赖冗余**：frontend/requirements.txt 中包含 Python 内置模块
- **缺失依赖**：某些功能可能依赖未在 requirements.txt 中声明的包

### 3.4 具体 Bug
```python
# frontend/requirements.txt 中的问题
asyncio    # 这是 Python 内置模块，不应在 requirements.txt 中
uuid       # 同上
json       # 同上
datetime   # 同上
typing     # 同上
traceback  # 同上
```

## 4. 实际优化建议

### 4.1 架构层面优化

#### 4.1.1 引入设计模式
```python
# 建议使用工厂模式管理不同的 Pipeline
class PipelineFactory:
    @staticmethod
    def create_pipeline(pipeline_type: str, **kwargs):
        pipelines = {
            "llm": LLMQA,
            "rag": RAGQA,
            "data": DATAQA,
            "car": CARQA,
            "all": ALLQA
        }
        return pipelines[pipeline_type](**kwargs)
```

#### 4.1.2 抽象基类重构
```python
# 创建抽象基类减少代码重复
from abc import ABC, abstractmethod

class BaseQAPipeline(ABC):
    @abstractmethod
    async def _retrieve_knowledge(self, query: str, user_id: str, **kwargs):
        pass
    
    @abstractmethod
    async def generate_stream(self, **kwargs):
        pass
```

### 4.2 安全性增强

#### 4.2.1 CORS 配置优化
```python
# 替换过于宽松的 CORS 配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "http://localhost:7862").split(","),
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["Content-Type", "Authorization"],
)
```

#### 4.2.2 输入验证增强
```python
# 添加输入验证装饰器
def validate_input(max_length: int = 1000):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            request = args[0] if args else kwargs.get('request')
            if hasattr(request, 'query') and len(request.query) > max_length:
                raise HTTPException(400, "查询内容过长")
            return await func(*args, **kwargs)
        return wrapper
    return decorator
```

### 4.3 性能优化

#### 4.3.1 连接池优化
```python
# 优化 HTTP 客户端连接池
import httpx

class OptimizedHTTPClient:
    def __init__(self):
        self.client = httpx.AsyncClient(
            limits=httpx.Limits(
                max_keepalive_connections=50,
                max_connections=200,
                keepalive_expiry=30
            ),
            timeout=httpx.Timeout(60.0)
        )
```

#### 4.3.2 缓存策略优化
```python
# 实现分层缓存
class LayeredCache:
    def __init__(self):
        self.l1_cache = {}  # 内存缓存
        self.l2_cache = redis_client  # Redis 缓存
    
    async def get(self, key: str):
        # 先查 L1 缓存
        if key in self.l1_cache:
            return self.l1_cache[key]
        # 再查 L2 缓存
        value = await self.l2_cache.get(key)
        if value:
            self.l1_cache[key] = value
        return value
```

### 4.4 监控和可观测性

#### 4.4.1 指标收集
```python
# 添加 Prometheus 指标
from prometheus_client import Counter, Histogram, Gauge

REQUEST_COUNT = Counter('api_requests_total', 'Total API requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('api_request_duration_seconds', 'API request duration')
ACTIVE_CONNECTIONS = Gauge('active_connections', 'Active connections')
```

#### 4.4.2 结构化日志
```python
# 改进日志格式
import structlog

logger = structlog.get_logger()

# 使用结构化日志
logger.info("API request received", 
           user_id=user_id, 
           endpoint=endpoint, 
           request_id=request_id)
```

### 4.5 错误处理优化

#### 4.5.1 统一异常处理
```python
# 创建统一的异常处理器
class APIException(Exception):
    def __init__(self, message: str, error_code: str, status_code: int = 500):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code

@app.exception_handler(APIException)
async def api_exception_handler(request: Request, exc: APIException):
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            success=False,
            message=exc.message,
            error_code=exc.error_code
        ).model_dump()
    )
```

### 4.6 配置管理优化

#### 4.6.1 配置验证
```python
# 添加配置验证
from pydantic import BaseSettings, validator

class Settings(BaseSettings):
    api_base_url: str
    redis_host: str
    redis_port: int = 6379
    
    @validator('api_base_url')
    def validate_api_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError('API URL must start with http:// or https://')
        return v
    
    class Config:
        env_file = ".env"
```

## 5. 功能层面分析

### 5.1 性能提升空间

#### 5.1.1 检索性能
- **并行检索优化**：当前多库检索虽然使用了 `asyncio.gather`，但可以进一步优化批处理逻辑
- **缓存命中率**：检索结果缓存策略可以根据查询相似度进行优化
- **索引优化**：建议对知识库建立更精细的索引结构

#### 5.1.2 生成性能
- **流式优化**：流式生成可以实现更细粒度的控制
- **模型调度**：可以实现智能的模型选择和负载均衡
- **预计算**：对于常见查询可以预计算结果

### 5.2 稳定性提升空间

#### 5.2.1 容错机制
- **重试策略**：当前重试机制较为简单，建议实现指数退避
- **熔断器**：对外部服务调用添加熔断器模式
- **降级策略**：在服务不可用时提供降级服务

#### 5.2.2 资源管理
- **内存控制**：对大量检索结果进行分页处理
- **连接管理**：实现连接池的健康检查和自动恢复
- **超时控制**：为所有异步操作设置合理的超时时间

## 6. 代码规范评估

### 6.1 符合标准的方面
- **类型注解**：大部分函数都有类型注解
- **文档字符串**：核心函数有较好的文档说明
- **模块化设计**：代码结构清晰，模块职责分明
- **异步编程**：正确使用了 async/await 模式

### 6.2 需要改进的方面

#### 6.2.1 命名规范
```python
# 当前存在的问题
API_ACCESS_TOKEN = get_api_access_token()  # 全局变量应该避免
print(f"API_ACCESS_TOKEN: {API_ACCESS_TOKEN}")  # 调试代码应该移除

# 建议改进
_api_access_token = None

def get_cached_api_token():
    global _api_access_token
    if _api_access_token is None:
        _api_access_token = get_api_access_token()
    return _api_access_token
```

#### 6.2.2 代码注释
```python
# 当前存在大量注释掉的代码，建议清理
# # app初始化之后，初始化opentelemetry
# access_key = os.environ.get("access_key","")
# access_secret = os.environ.get("access_secret","")
```

#### 6.2.3 错误处理规范
```python
# 当前的错误处理不够统一
try:
    # 业务逻辑
    pass
except Exception as e:
    # 应该捕获具体的异常类型
    logger.error(f"错误: {str(e)}")
```

## 7. 具体实施建议

### 7.1 短期优化（1-2周）
1. **清理依赖文件**：修复 frontend/requirements.txt 中的问题
2. **安全配置**：修改 CORS 配置和敏感信息处理
3. **代码清理**：移除注释掉的代码和调试语句
4. **异常处理**：统一异常处理机制

### 7.2 中期优化（1-2月）
1. **重构 Pipeline**：抽象公共逻辑，减少代码重复
2. **性能监控**：添加详细的性能指标收集
3. **缓存优化**：实现分层缓存和智能缓存策略
4. **测试覆盖**：提高单元测试和集成测试覆盖率

### 7.3 长期优化（3-6月）
1. **架构重构**：考虑微服务拆分
2. **可观测性**：完善监控、告警和链路追踪
3. **自动化**：实现 CI/CD 和自动化部署
4. **文档完善**：补充 API 文档和运维文档

## 8. 总结

该项目整体架构合理，功能完整，但在安全性、性能优化、代码质量等方面还有较大提升空间。建议按照上述优化建议分阶段实施改进，重点关注安全性和稳定性的提升。



2025-07-19 14:04:37.855 | ERROR | request_id=YOU_UUID | api.routes:generate_stream:358 | DATAQA流式生成错误: cannot access local variable 'response' where it is not associated with a value，耗时: 60.01秒


curl -X 'POST' \
  'http://s-20250717115833-ihp9w.ak-cloudml.xiaomi.srv/v1/chat/completions' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer sk-dms-Qwen3-32B-2025-07-17' \
  -H 'Content-Type: application/json' \
  -d '{
  "model": "Qwen3-32B",
  "stream": true,
  "temperature": 0.5,
  "top_p": 0.5,
  "max_tokens": 2048,
  "messages": [
    {
      "content": "string",
      "role": "developer",
      "name": "string"
    },
    {
      "content": "你是一个专业的问答助手",
      "role": "system",
      "name": "string"
    },
    {
      "content": "你好",
      "role": "user",
      "name": "string"
    }
  ],
  "return_tokens_as_token_ids": true,
  "additionalProp1": {}
}'


curl -X POST "http://10.29.227.159:8000/v1/chat/completions"   -H "Authorization: Bearer sk-dms-Qwen3-32B-2025-05-23"   -H "Content-Type: application/json"   -d '{
    "model": "Qwen3-32B",
    "messages": [{"role": "user", "content": "Hello, world!"}],
    "temperature": 0.7,
    "top_p": 0.95,
    "max_tokens": 512,
    "stream": true
  }'


curl -X POST "http://s-20250717115833-ihp9w.ak-cloudml.xiaomi.srv/v1/chat/completions"   -H "Authorization: Bearer sk-dms-Qwen3-32B-2025-07-17"   -H "Content-Type: application/json"   -d '{
    "model": "Qwen3-32B",
    "messages": [{"role": "user", "content": "Hello, world!"}],
    "temperature": 0.7,
    "top_p": 0.95,
    "max_tokens": 512,
    "stream": true
  }'



  curl -X 'POST' \
  'http://s-20250717114025-1tpip.ak-cloudml.xiaomi.srv/rerank' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer sk-dms-qwen3-reranker-2025-07-17' \
  -H 'Content-Type: application/json' \
  -d '{
  "model": "qwen3-reranker-0.6B-dms-2025-07-17",
  "query": "你好",
  "documents": [
    "hello"
  ],
  "top_n": 0,
  "truncate_prompt_tokens": 1,
  "additional_data": "string",
  "priority": 0,
  "additionalProp1": {}
}'


# Rerank API 配置
RERANK_API_URL=http://***********:8005/rerank
RERANK_API_KEY=sk-dms-qwen3-reranker-8B-2025-06-11



{"id": "*****************", 
"content": "", 
"user_id": "", 

"metadata_json": 
{"doc_id": "399708", "doc_type": "doc", "publish_time": "2024-10-18 19:24:03.0", "project_area": "013\t", "secrecy_level": "secret", "doc_url": "https://xiaomi.f.mioffice.cn/docx/doxk4hJnq7KC4WWdAP9QoX6ePKd", "doc_name": "M1 项目结构复盘报告", "tm": "2025-07-09 23:58:32", 

"owner": "

{"account":"王梓牟","avatar240":"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00ko_3c2e5da5-9647-4697-8ec0-7312283bdceg~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp","avatar72":"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00ko_3c2e5da5-9647-4697-8ec0-7312283bdceg~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp","createTime":*************,"dept":"手机部-硬件工程部-工程开发部-北京开发一部-结构工艺部","email":"<EMAIL>","hrStatus":"A","id":1207498,"openId":"ou_89ed367cb58dd374148f3a7e61802789","password":"","updateTime":*************,"username":"wangzimou"}"

, "create_time": "2023-03-29 15:09:13", "create_user": "wangzimou", "update_time": "2023-07-11 21:03:11", "update_user": "", "source": "ipd", "chunk_idx": 0, "n_chunk": 1}

, "score": 0.*****************}


curl -X POST http://search.misearch.test.b2c.srv/query   -H "Content-Type: application/json"   -d '{
    "user_id": "user_id",
    "db_name": "",
    "token": "",
    "query": "PCB Design Checklist",
    "additional_params": {
      "top_k": 2,
      "search_mode": "hybrid",
      "fusion_method": "rrf",
      "sparse_weight": 0.3,
      "dense_weight": 0.7,
      "rrf_k": 60
    },
    "collection_name": "knowledge_base_hardware"
  }'





面试官
19:00
哈喽，你好，可以听到吗？

刘永强
19:01
诶，可以。

面试官
19:01
好的，你好，我是那个小米的面试官面试官，然后你面试的这个岗位是那个大模型应用算法岗位。

刘永强
19:01
诶，好像是的。

面试官
19:01
嗯，好的，给你介绍一下咱们的面试流程，分三个部分，首先是你需要做一个那个自我介绍，然后咱们会聊一下你的那个工作经历，还有项目经历，最后会有一道那个算法题的一个考核啊。

面试官
19:01
嗯，好的，那你先做一个自我介绍吧。

刘永强
19:01
那面试官晚上好，然后我叫李永强，16年本硕被吉林大学，然后以校招该加入美团，然后是在酒旅事业群做那个促销的系统。然后是19年是加压加入博人联动赠送开发，然后是，然后再加那个，那个是22年，是对，那个国信科技是做一个代码变成助手大模型，然后接下来是22年的10月份，是在那个升维置信，是做那个诶？

刘永强
19:02
就会话智能会话分析，然后二三年是加入到我们现在这个猎豹移动是做这个大模型训练微调，然后相关的一些团队里面，对，最近是半年是开始做这种类似多币的 live agent OS 相关的一些工作。

刘永强
19:02
好，这就是我的一个大致的一个介绍。

面试官
19:02
嗯，行，好的，你方便说一下那个看接口的原因吗？

刘永强
19:02
就是我们这个团队的话，就是当时预期的老板的话是一个战略性的一个项目，一个团队，然后做预训建，然后做微调，然后也开源了一个 audience b 的，包括 Moe 的模型，然后其中也有一些 ToB 的项目跟公司 b 项目，然后但是这个效果其实是没有达到公司的预期，就是可能这个团队的话就是处在一种亏，一直亏钱的状态也没有好的变现的一个状，变现的一个途径。

刘永强
19:03
所以说公司可能就在他们新车团队现在目前就是基本上就是暂停状态了，然后大家可能就要不一部分转转，一部分转那个应用，其实我也是转开始，在这部分已经开始也有半年时间，然后应用的话，其实现在公司这个应用的话，其实因为我们公司是机器人，然后这个应用的话其实它的场景上并不是很多，所以说可能没有一个好的，一个也没有一个好的一个变现的一个方式，然后我其实想做一些大模型。

刘永强
19:03
我的想法就是想一起是能做一些大模型相关的东西，然后做应用其实也可以，也自己也是可以。但是希望找一个比较大的平台的，能够有一个场景上能够说有个好的场景是落地的场景，然后对，能看到自己做的东西这个效果落地效。

面试官
19:03
嗯，行，好的，了解。那等于是你们之前做的可能是偏那个模型训练这一侧，然后你也不排斥应用方向，是吧？

刘永强
19:04
对。

面试官
19:04
行，好的，那咱们主要围绕着那个恒大模型有关的这些那个项目经理怎么连？来聊一下，然后你先介绍一下。

刘永强
19:04
OK。

面试官
19:04
对，你先介绍一下最近的这几段，那个和大模型有关的项目经理，稍微展开介绍一下。

刘永强
19:04
OK，然后我，那我就是整个一下这个猎豹的这个左右的所有的这工作，然后我进去，是吧？是因为我们，当时我招进去是因为，是因训练，然后微调这个开源模型去招的。当时静姐就是开始做一训练，就是相当于是开始是负责这个搭某些整个的一些评测，包括效果的一些，包括一些模型训练过程中有什么问题，然后去做这种主观，我这客观的一些频次，然后去分析这些模型的一些能力，还有后面的话预训练，后期的话是去针对模型的存在的问题，然后做一些数据的调，模型的调优，然后舆情量结束，保底14。

刘永强
19:05
舆情结束以后就是开始直接走这个大模型的一个微调，就我们开源模型的微调，在阿里这个模型微调大概是在24年1月份基本上接受了，然后我们就开源这个模型，开源模型以后，然后我们公司就开始进入到下一个阶段，是进，因为我们这个 audience 撕逼效果还是不错，然后同时公司就说我们就当时那时候 m o moe 也比较几个趋势，然后我们公司就开始转到这个训练。

刘永强
19:05
这个八成7B 的 Moe 的模型，然后我的这里面的话，其实这个 Moe 模型上，其实大家我们当时都是轻车输入，然后我当时，然后在里面是一个建一个，并不是一个完全是在去做这个 moe，然后我是去在 moe 里面分担一部分决赛，然后还一个就是我其他的任务是做那个几把几，类似我刚才说的，然后公司是有一个变现任务的，就是做作弊的一个项目，就是更繁入的一个大华住的一个大集团的合作。

刘永强
19:05
做了一个数值问答，就是类似于是我们一个数据智能分析类的那个数，智能问答就是一个 NL 度读一个 take date，类似于说我一个自营源 query，然后我能出，想要出我这个结果，类似说今年营收怎么样，然后今年应收为什么下降了？

刘永强
19:06
类似于这种数值问答的一个项目，然后这中间也会参与这个 Moe 的一些工作了，后面的话是也接也做了，一直在做华州的，然后包括质量带教的一些工作，就是去年大概9月份，然后最近11月份的话是做那个再把 OT 里面的话用它的技术去用我们的 Moe 模型去训练一个去训练包括微调一个垂直领域的一个模型，这个模型的话现在也相当于交付了。

刘永强
19:06
然后最近的话，这半年的话应该是，嗯，不到半年五个月，然后是做这个 agent OS，我们，因为我们公司是也把我们，然后有个下面有个子公司叫猎户，然后我们这两个机制是在一起的，就是工作的一个团队在一起的都是机器人，机器人的话叫，然后这机器人，然后我们就我们在这上面做一个 agent OS 系统，就是对，这个主要是即使所有的公司这个战略绩效我其实都是在参与的，基本上也是算一个核心角色吧。

面试官
19:07
嗯，行，好的，了解，就是你刚才介绍这几段我看一下。22年的那一段是在那个身为这行，

刘永强
19:07
不行，

面试官
19:07
你是吧？然后最下面那个项目2，

刘永强
19:07
22年是。

面试官
19:07
对，那个百亿级代码那个是在上一家公司，然后最近的这几段都是在那个最近的这个公司，是吧？

刘永强
19:07
对，那个代码模型是归星科技，对。

面试官
19:07
嗯，行，好的，了解，那咱们先聊一下那个猎豹的这些经验，然后你们训练预模，预预训练模型是从那个23年10月份就开始做了，是吧？

刘永强
19:08
对。

面试官
19:08
你们是从零开始做的吗？

刘永强
19:08
对，0，从0开始找。

面试官
19:08
23年10月份，当时应该已经开源出来一些模型了，是吧？

刘永强
19:08
我是23年一10月份是到入职了这个猎豹，但是这个项目开始的话是公司立项的话是6月份，六七月份。我理解，然后其前期大概两个月是做那个实验，包括去在当时是一个 MA 上面也做了一个继续的一个训练，包括增量训练、微调，还有用小模型去从零一到审训，大概实验了两个月，然后真正开始是从9月中旬开始这个舆情点大模型的，然后从你开始训练，报告是前期，他们是从6月份开始也去做数据处理，从9月份开始进行训练。

刘永强
19:08
对，我相当是前期预训练，前期加入的，

面试官
19:08
你是在明白，

刘永强
19:08
对不？并不是从头可以把这东西。

面试官
19:09
那你清楚，就是你们为什么要从头训练一个这样一个预训练模型，而不是选择做那个后续练那些工作呢。

刘永强
19:09
就是我，我大概懂你聊过，就是老板聊过，就是我们当时想做大，就是说我们是当时公司赚的，就是快去做开业模型，然后开业模型，我模型开业周期感觉就能一直看一，然后自己也懂整个这一列那个技术，包括数据抓取数据，然后从如何从零开始训，然后他说这个技术栈我们都得把握，然后包括我们要，一定要，而且我们自己信自己的模型，然后我们能够迭代，然后给 b 的时候给别的公司做增量训练什么的。

刘永强
19:09
给，对于我们自己知识产权这个是他们是这考虑的，但当时考虑的是这条路能够走通，然后就说我开言出去以后有商家、有企业来找我，那企业有自己的定制化需求，那我们就可以给他做，我们有自己拿到自己，我们自有自己的训练数据。

刘永强
19:10
那我们可以给他进行继续训练，继续微调，这个都是，这都是有的，如果你只是增量训练的话，首先你不一定能在别人模型上开去增量训练，它也不一定可以，就现在已经其实像先问它，它算开源，像像那像它那个协议它算是可能够进行商业化了。

刘永强
19:10
但是之前的话是不知道后面是发生什么样的，但是公司当时站点就是这样，就是我们如果走这条路，要大家这个这条路比较有方，比较有前景，那他也就看得比较长远一点，然后要走这条路。

面试官
19:10
嗯，行，好的，了解。那你们这个模型是只训了一个14B 的，是吧？

面试官
19:10
100不是140亿，

刘永强
19:10
14B 的，然后暂时模型。

面试官
19:10
是14亿。

刘永强
19:10
对，但是模型是14B，然后有个 moe 模型是8×7 b。

面试官
19:11
OK，但是模型14B 你们是训了多久呢？在什么？多少张卡上来训的呀？训了多久？大概。

刘永强
19:11
是16章的 H800在阿里云的平台上，然后信的话前前后后信的大概有，预训练的话大概有4个多月，四五个月吧。

面试官
19:11
玉轩来训了四五个月，是吧？你们前期有踩坑吗？就续模型的时候。

刘永强
19:11
就是说你是说真正开始运行的14例模型的事有没有踩坑，是吗？

面试官
19:11
对。

刘永强
19:11
对，就是踩坑，肯定是有一些坑，有些坑的话是因为我们的话，因为我们就首先分这个地方要回答这个问题的话就是回答两部分。首先我们前期不是有实验，然后自己也从小模型开始，b 的模型也训了，快速训的这个地方其实能够包括当时说去看看哪个模，嗯，哪个框架是说对比速度，哪个吸量快一点，哪个吸烟慢一点，当时是就对比了几个框架，之后选了 Mac 转，然后它还是比较快的，然后我说类似这才数据里面怎么去配比数据？

刘永强
19:12
怎么模型结构？怎么设计？这个地方是在做实验的时候去集中的，包括说我这个 embedding size，包括我 vocabus size 这个东西，包括这个 organizer，然后训练都是在前期，后期的话是会遇到说因为我们的训练是分阶的，分五部分，我们数据并不是一下准备好的，我们是分五个阶段准备数据的，然后每个阶段准备一些数据，然后大概因为数据并没准备那么快，然后所以说在每一阶段我们都会去看每个，就是每天基本上都会看模型的一些变化。

刘永强
19:12
然后是不是哪个用变动了？哪个用变好？最后一个变了，然后踩坑的话就是类似当时就是最主要的一个问题，就是训练不稳定，就是类似于说这个数，一个是新内部稳定，一个是说就是 loss 容易标，就是可能就我们当时遇到的问题就是震荡，就是一直不是很平稳，就是震荡，但是那个地方是当时我们也找了一个解决方案了，找到一个问题了，然后在那，然后还一个就是数据里面可能会存在一个数据。

刘永强
19:13
就是中间国庆的时候，国庆之后，然后也出了个问题，就数据的话，那他弄了一个问题，然后最后通过测试时候发现，然后他把那个数据如就修复了，抛掉了，然后就把那个那个地方就相当于也是一个坑，然后就数据部分。

刘永强
19:13
还一个就是可能就是我们最主要的数一个问题，就是 tokenize 的时候，就是最终这个是到那个最终训练完以后，因为 tokenizer 你发现是它问题，你中中途不能改，然后你如果是数据有问题，你可以通过数据再增加一些数据，再重新训一训，再重新调整一下。

刘永强
19:13
但 organizer 当时发现一些问题，那个地方它就是一个比较大的一个调性，就是标点符号，标点符号没有区分中英文这些东西，然后导致但是这个东西开源出去以后是通过后处理的方式去解决的这种东西，对。

面试官
19:14
行，好的。

刘永强
19:14
对。

面试官
19:14
了解吗？

刘永强
19:14
真 4 的话，理解就是它可能到了一个就是数据的话，就是拟合的过程中可能拟合的不好，可能你这个数一个是数据造成比较大，这可能会导致它就是他来回拟合，就是就拟合是不能数据造成比较大，还一个就是他可能在到了一个那个到了某个区域，就是到了，就是我们是类似于说它那个损失曲线里，损失那个曲面里面可能到了一个比较震动，我说一个极小值有可能在类似一个优化器里面。

刘永强
19:14
如果它来回去做个震荡，就类似于会有这种问题。对，

面试官
19:15
行。

刘永强
19:15
那这两个都遇到过，这两个就是都遇到过，就是如果他到那个点，那我们就回退几个，一个两个这个供应单。对。

面试官
19:15
嗯，行，好的，那你们这个预训练，你们那个数据都包含哪些数据呢？

刘永强
19:15
数据的话，首先我们英文的数据的话，基本上还是拿开源的。英文的话，英文我们并没有在英文上去花太多的精力去爬什么的，然后，嗯，开源的话我们理解就有 start go 的，然后有拍这 pipo，然后这些东西做得比较大的，当时那个时候，而且是足够你去训练中文的话，我们是一个是开源自己清洗，还有自己技术业绩下级，嗯，可能会通过一些技术手段自己爬一下数据，还有是满这几个，然后数据类别的话，一个是首先是那个 code 加 mass。

刘永强
19:15
我们可以理解它是属于推理能力这一块，逻这个数据，然后还一个是那个页面的文燕的，有网页的页面的类的数据，就是还有类似于说还有的就是类似维基百科、百度百科这些知识性比较强的，还有书籍类的数据类的，还有那个就是题库，就是各种就是类似于猿辅导，然后他们有那种各个学科的题库，我们会去抓出来，诶？

刘永强
19:16
有类似于评论的数据也会抓，就是那个知乎这个这些数据也会抓。对，主要是这些。

面试官
19:16
行，好的，了解。你们这个模型，看，你说那个是比当时那些开源模型那个在一些指标上要好，那你觉得你们这个比他们要好的一些，那个底层的原因是什么呀？

刘永强
19:16
好，底层主要是首先我们架构基本上跟那个别的模型基本上跟拉满2是差不多的，然后我们可能的，嗯，训练过程中优的一个不同点是我们是分阶段的集训，然后会根据模型现在训的是训的情况，然后我们去做一个数据配比，这是一个肯定一个点，还有一个点就是在后续的优化的阶段的话，我们是对模型的能力去做，真有会带他的一些落点、一些弱项，我们也会去做他一些调优。

刘永强
19:17
包括一些调整，一些调优这方面的东西。其实说我们理解的话，大模型的像训练的话，预训练的话，在那个年代那个地方这架构，其实大家对架构的魔改其实还是不是很多了，那个就是你的数据实际的问题，你能不能有一个大家数据可能说我们初步的数据从往后从拔数据都差不多，但是你能不能训练一个好的质量模型？

刘永强
19:17
你要确定好的执行模型，你是你的数据质量就非常高，你数据质量高，你的那个效果就好，还有一就是你的数据配比，你的数据是怎么去增加它的高效能力的？推理能力，高效能力也知识能力，写世界知识能力，这个就是你的数据，你可以包括你数据的一个组合问题。

面试官
19:18
行，好的，了解等于是模型架构方面和那个拉马13比差不多，然后你们可能就在数据训练层面你们做了一些策略，是吧？

刘永强
19:18
对。对，还有一点就是我们在那个人力类的方面，就是我们收敛的话比别人快，就是不是15点就要下降，就是我们当时就是让他 landing net 变大，然后不同阶段用不同 LANDING net 这个策略也是我们自己实现出来的。

刘永强
19:18
就是说我给这么多 TOKEN，嗯。

面试官
19:18
你们是，那你们训练分了几个阶段呢？

刘永强
19:18
就是预训练的时候是有五批数据，五批数据的话就是，但这五批数据里面可能会针对这个模型的能力，就是我们会拿，那我们落实在训练这个 OK TOKEN，然后我们落实跟类似的百川2对比一下，我们是不是效果比得好？

刘永强
19:19
然后我们 loss 跟它比这个地方有没有降？是不是这个 loss 体现？是不是变频缓了？变频缓以后，那我再考虑是不是它已经饱和了，我在这部分数据是不是已经饱和了？

刘永强
19:19
那我们是略换一批别的数据，你的就是说像你如果一直用那个网页页面的数据，它基本上都见过了，然后你再训，其实它 loss 其实不怎么下了，瞎讲，我瞎讲还会很慢。你肯定要放一批其他的数据，让他 loss 再下降，这个地方你要实时感知，就模型训练状态。

面试官
19:19
你们在不同阶段训的话，你们会调整模型参数吗？

刘永强
19:19
这个不能，这个不会，这个可能这个不能跳，你知道超参数是吗？还是说模型？

面试官
19:19
有挑挑层。

刘永强
19:19
超参的话 LANDING light 肯定会调，其他的话基本上就。嗯，没有太大，没太多问题调的。

面试官
19:20
嗯，行，好的，那个学习率你们是怎么调的这个？

刘永强
19:20
学习力怎么调？是吗？

面试官
19:20
对，就是，嗯。

刘永强
19:20
就是你要是看到他的一批一批数据，然后他现在慢慢，因为我们是以当时我们设置的话是一个 ADW，他一个那个 Cosine 的一个曲线，然后它可能会，可能是为在后面下降得比较快，下降得比较快的话你可能参逻，那你那里比较小，这就是可能新的后面 learning 那里比较小，然后但是你的数据可能是一个比较新的数据，我是一批新的数据，你那时候也可能把人类学调大一点。

刘永强
19:20
然后你在某些数据时候，如果这批数据，如果你觉得这批数据质量比较高，我希望他拟合到这批数据上，那我希望把这个链接调大一点，但是到后期的话，那我可能要在后期的话，基本上模型已经收敛了一下，指标给打上去了，今年也差不多了，那我们后期让他训练一点，就让他学更稳一点，就是不要受，不由你和数据太多了，我现在时间长一点，这个肯定低一点，就让他去在各个数据的见过。

刘永强
19:21
然后让他就是能够拟合，就是能够不要有太被某一批数据或者某一类数据去影响。然后就会在这方面就会低一点。上面第一就是这种尾巴那个阶段，就是让他就是多学时间长一点，然后让他多去看一些数据。

面试官
19:21
行，好的，了解。

刘永强
19:21
就是这个，还有个词，其实有个就是经验，就是在训练过程中，因为大部分预训练的话都是训练人数据吸引力，POG 就不可能训两个，因为那个不划算。但是在如果说你，但是对高质量数据的话，这批数据们肯定就像高质量数据，那个是会信，可能会信两个 POG，或许甚至两三个 POGO，那就是把那个捞出来，然后再加到那个通用数据里边。

面试官
19:22
嗯，行，好的，那你们这个模型，它那个他几乎函数用的是什么函数你知道吗？这了解吗？

刘永强
19:22
什么？group，然后是用，是 group。

面试官
19:22
激活函数。如是吧？你们为什么选择这个结果函数呢？

刘永强
19:22
这个当时应该是沿用的 MA 那个结构吧。

面试官
19:22
拉码应该是他用的，不是原生的好像。

刘永强
19:22
你说只原生。是是是，你说拉吗？原拉没用，原生的是什么？

面试官
19:22
就是他用的是那个激活函数的一个变体。

刘永强
19:22
你说他用的不是 clue，是吧？

面试官
19:22
对。

刘永强
19:23
那个，那这个细节我还确实有点忽略。

面试官
19:23
行，好的，那我也，我大概记得好像是他用的做了一些改动啊。行，那咱们不聊这个了，然后你们这个是做完预训练以后，你们又作为调，是吧？

刘永强
19:23
对。

面试官
19:23
做微调的话，你们都是做了哪些方面的微调呢？

刘永强
19:23
微调的话就是一个 SFT，加上强化学习 RO SFT 的话其实主要是还是在那个数据质量问题了，

面试官
19:23
对。

刘永强
19:23
就是怎么算数据？怎么去过滤数据？高质量数据，然后 d，然后 RO 的话当时也没上那个 PB，要复杂算法后面是有实验这个，但是主要是我们当时在，因为时间比较短，然后用 DPO 算法，然后去把这个用 DPO 算是比较 DPO 是在这个，因为在这个资源，在这个我们资源语言里面，我们文本里面还算是可以的，因为它不像一些代码模代码的推理这方面的，对吧？

面试官
19:24
嗯，好的，那你们做微调的时候，因为微调的话其实有很多下游任务，因为我不太清楚你们这个模型它们面向的下游任务主要是哪些？你们有针对不同类型的下游任务来做这个微调吗？

刘永强
19:24
我们是个通用模型，通用模型就是就不会针对某一个垂直领域的一个任务去做，就是开源数据的就是各个任务都有，主要是需，主要是微调它的指令更新能力，就是问答能力，就是如果你要，如果我这开模型，然后拿到企业中心用的话，你可能企业中还有需要用他自己的一些数据进行微调。

刘永强
19:24
就是我们只针对一个通用能力，包括一些那类似于乘以问答案，然后写一个，就是类似一个，我就是类似一个平常的问题，然后一个问答 QA，对，还有类似于这种，这类类似这些就是这。

面试官
19:24
嗯，行，好的，那你们那个微调的方法是什么呀？然后你们用了大概是多大量级的那个微调的一个数据量？

刘永强
19:25
微调的方法指的是。

面试官
19:25
对，你们那个微调，你们做完预训练以后，你们是做全参数微调吗？

刘永强
19:25
对对对。

面试官
19:25
那你们用了多大那个量级的一个数据量？

刘永强
19:25
是10万级，10万多一点，11万左右。

面试官
19:25
11万是吧？11万，然后做完微调以后又做 DPO，是吧？

刘永强
19:25
对。

面试官
19:25
嗯，好的，你们这个模型是就在那个24年2月份续完以后，在之后就没有再继续迭代继续训了，是吧？

刘永强
19:25
没有。

面试官
19:25
然后是我看你是在24年9月份开始去那个 Moe 的那个模型。

刘永强
19:26
那是一个华住领域的，垂直领域的一个 moe emoji，然后我们的那个开源的模型，Moe 的模型是2月份开始性24年2月份就是等这个我们自己开源的模型迅速训完了，大概是八九月份训完了，然后我再我们自己的那个，我们自己公司训的那个模型上去做一个垂直领域的一个区域训练，区序微调区域训练。

面试官
19:26
明白，等于是你们那个公司从24年2月份以后有再去那个 Moe 的那个模型，然后只是你没有参与那块工作，是吧？

刘永强
19:26
对，我餐的餐饮不多，对，基本上可以认为是对。

面试官
19:26
行，好的，了解。DPO 那个 RPO 有了解过吗？

刘永强
19:26
有，就那个有。

面试官
19:26
对，那你可以简单说一下那个 PG RPU 和之前那些 PPU DPO。他的方法上他是做了哪些调整改进？

刘永强
19:27
PP 要是这个，对，就是它 PIPO 其实就是为了改进那个 PBO 的，它的问题就是 PBO 的话，他的问题一个是他的训练不稳定，第二个就是他的训练的代价就是代价很高，因为他是有四个模型的，四个模型里面有两个模型是要做梯度就下降的，就类似于那个 edit 跟那个 critical，它是要做梯度下降的，然后 Lark 跟那个 reward 模型是，这是不用，但是它需要加到浅层里面。

刘永强
19:27
就是它在他的今天代价是很大的，还有一个点就是他因为有这种模型，所以他有很多的超参数，而且超参数这些新的结很敏感的，所以说他的训练是很不稳定的，类似于说还有一个说你要是就是你 word 模 word，你这里面控制不稳，有影响，不稳定的时候还是有很多的，就他就是导致说大家就没法，很难去训练出来，一，很难去用好它，然后后面其实有很多就类似于是针对 PBO 去改进的。

刘永强
19:28
就大部分改进的话是把那个，嗯，那个 clinical 那个模型，就是尽量把它给剪掉，然后那个，对，然后还有那个那个 level 模型一，大家一，就是类似要那个 DPO 去把它剪的，把它剪掉，然后 GRPO 的话，我理解它也是把它剪掉。

刘永强
19:28
然后可以第一个他减掉的话，他是那个 GRPO 的话，他的主要改进点是说他它有两个主要的改进点，一个是在那个 l 散度方面，l 散度就是让我训练的模型跟原始的模型 Lark 模型不要差太大，它是皮皮，要是为了这个意图，但是皮皮要的它是把它放在那个 word 那个计算里面去了，然后它用的还是说要去计算它们俩之间的一个分布？

刘永强
19:29
然后但是那个 GRPO 它是把没有把它放到 word 里面，那是把它放在那个最终的一个目标函数里面去，然后它在计算 KR KOL 散，做计算的话它是一个预估的一个去估算法，然后并没有说直接说去计算两个人，两个模型的一个之间的1分布，然后它是有一个估算函数，所以它这个就是节省了很大的一部分那个计算量。

刘永强
19:29
然后还有个就是 RPO 的话，一个重要点就是我们知道它们俩都是从一脉上，一脉下来，它都是用梯度策略，梯度策略来去估算这个皮标的一些损失函数，然后但是 GP RP owner 是通过他为了稳定性，他是说一次一个 poly，他是生成多个答案，然后通过多个答案进行求兵，进行一个组的概念，然后让这个模型不要因为某一些数据，某一数据突然这个 loss。

刘永强
19:29
不要突然 liword 突然变大或变小，或者拧额到某一类数据上。它是通过这种生成一下，生成头条数据，然后通过一些你聚合的方式，然后就那保证拆这一个稳定性。对，然后还有包括像我就通过一些有个优势函数的话，

面试官
19:30
模块。

刘永强
19:30
我是要减掉一个 base，我是，我不是让它直接是去既，让它去拿这个 reward，你因为这个如果一个模型，如果只是一拿 word 话，有些 reward 是比较稀疏的，

刘永强
19:30
比较清楚，类似于特别语言模型比较稀疏，然后有些那个礼物的话它是很难抠得出来的，然后还有的是礼物的，单个礼物的话你可能会收到噪声影响，它会变动比较大。

刘永强
19:30
对，然后还有他的 GRPO，他自己发现一些策略，就是他还有发现一些策略，就是在整个句子级别，结果级别进行一些。这，这次你传中心，这，这这个你 water 函数进行计算，然后还有发现的排名一些那个规则的，通过规则的方式计算一些 reward，包括一些回答风格格式，包括一些类似于代码跟模型代码跟那个数学，它会通过一些通过单，是否通过单元测试是否是它？

刘永强
19:31
答案是否对，通过这种方式，通过规则的方式给它 word deward 结果。

面试官
19:31
嗯，行，好的，了解那个千问最新开源的那个模型有关注到吗？

刘永强
19:31
千万三是吧？

面试官
19:31
对，他上上月底又放出来一些新的一些模型。

刘永强
19:31
清餐。有关注就是千万三，他的话千万三是有个叫 P3，是5B 那个，他当时是有一个他最大的一个，那个他最大的一个就亮点就是叫 DG 的，就是预算，就是 TOKEN package 的，就是可以说我让你有这个新梗，有个预算，然后那个还有个流式输出的那个。

刘永强
19:32
然后的话我们也测了，我们反正一些开业模型出来以后，我们会立马去测它的效果，然后大概就是反正那个东西 APP 35那个35B 那个还是不如它那个切换 Max 效果好。

刘永强
19:32
但是我们也不知道切到 Max 到时候背后用的什么什么技术，他们是没有开源。

面试官
19:32
你说没有他们官网那个 m Max 好，是吧？行，好的。

面试官
19:32
那怎么接下来聊一下那个 Moe 的这个垂域模型，然后这是你们在那个你们做的那个 moe 8×7 b 的 base 模型基础上，你们做了一个微调，是吧？酒店旅游相关的数据这块你稍微展开介绍一下预训练，

刘永强
19:32
预训练异性恋加微调对优训练的话，

面试官
19:32
预训练价值。

刘永强
19:33
对异性链的话就是在找了一批那个酒店的数据，然后粉了我们通用的数据，大概训了30B，对，诶，

面试官
19:33
30。

刘永强
19:33
不是30B，诶，我们通用数据是30B，然后大概有四五十币，再加上10币的酒店通用数，酒店数据，酒店旅游数据，对，大概再加上代码数据，大概四五十币，然后用了50币去做一个增量预训练。

刘永强
19:33
然后再是微调的话，大概用了，大概是有个新增了，大概有个1万条到2万条的每条数据，还再加上粉上我们自己的一些微调数据。对。

面试官
19:33
OK，你们做增量预训练，数据配比就是通用领域再加代码，是吧？

刘永强
19:33
对，但再加上垂直领域的数据。

面试官
19:33
所以这领域的数据。OK，你们通用垂直领域数据配比大概是多少呢？

刘永强
19:34
大概是1:5。1:4，1:5。

面试官
19:34
通用比上垂直是1:5，是吧？

刘永强
19:34
通用大概是4，垂直是1。

面试官
19:34
通用是4，垂直是一。

刘永强
19:34
通用的话里面又分很多种，通用的话就是会分开，就是你又细分这些数据，包括代码的话，大概是当时说当时那个通用的话大概三四十米的话，代码模型可能也有5左右代码加数据。

面试官
19:34
OK，你们只是在这一个业务方向做了个微调预训练和微调，是吧？

刘永强
19:34
对，就是给每个公司它的9,001的一个大集团也算。

面试官
19:34
行，好的，了解。杜微调领域知识上提升10%。

面试官
19:35
这个秘书智能问数是做那个泰促 SQL 这一块，我看一下报告这个用的模型也是你们自己的模型吗？

刘永强
19:35
这个底层用的是两个模型，微调两个模型，两个模型是不是那个别的小模型？

面试官
19:35
小模型是吧？你们有作为条，是吧？

刘永强
19:35
对，有微调，在任务上做微调，构造数据微调，因为这个任务它是跟那个具体的业务是相勾勾的，你要是直接让它生成，效果很差的，当时因为它的要响应速度，强速的要求的，不能用太大的模型，当时用了7D 模型。

面试官
19:36
OK，七 b 的模型。take to circle text to code 7 in 的模型。诶这个你们用的微调的模型，

刘永强
19:36
其实。

面试官
19:36
你们是用的那种通用的模型还是用的那种 DE 模型啊？

刘永强
19:36
那个 code 就是我这个，就是这个智能问数的话，它是还有别的任务，就我这两个任务，嗯，他用的是 code 模型。

面试官
19:36
你这两个任务用的是 code 模型。

刘永强
19:36
别的那种类似。对，类似于洞察，然后那个语义提起这些东西是用的 text 模型洞察的，类似用，包括多轮的问题，多轮的一个，其实多轮的一个改写去用那个 test 模型。

面试官
19:36
多轮敢写 text 模型，多轮改写模型也做过微调，是吧？

刘永强
19:36
对，那个得做。

面试官
19:36
对，那你们都能改写作微调的话，你们是做完微调以后，那个模型只在只用来解决你们这个微改写任务，不会再用到其他任务上，是吧？

刘永强
19:37
就是多，就是 text，就是我们，我们是大概是五个任务，然后五个任务里面有三个任务是用 test 模型，两个任务透的模型就是有两个模型。

面试官
19:37
就两个模型，就是多任务的，是吧？行，好的，那你们在这个微调过程中，包括你们那个 test 模型和 ad 模型，你们有遇到什么问题吗？

刘永强
19:37
问题就是遇到就肯定要有个测试集，测试集的话你先微调，告诉他一批数据，然后你微调，然后要看看他效果，效果不好就分析分析。badcase，我一不开始，你可能在某类问题的回答不好，你就要就增加这类数据，就是就，其实就是构造数据，构造他哪一部分吹他不好分析 Badcase，然后去找出他的原因。

刘永强
19:38
主要原因，然后又针对性地构造一些数据。

面试官
19:38
你们这个用的微调方法也是。

刘永强
19:38
然后如果说这个，嗯。

面试官
19:38
你们也用的也是全参数微调，是吧？

刘永强
19:38
对。

面试官
19:38
对，你们没有试过其他的一些，比如 Laura 微调什么那种块拓展什么之类的微调方法嘛。

刘永强
19:38
那个的话没试过那个东西，那个，对，那个没试过。那个效果一般，之前在别的类目上试过，效果并不是很好。

面试官
19:38
行，好的，了解。OK，那咱们最后聊一下，那个你们最近半年做的这个 agent OS 这块东西。然后 agent OS 的话，机器人操作系统感知规划、执行三大核心模块，机器人思维链。

面试官
19:38
然后这个用的底层模型是什么模型呀？

刘永强
19:39
这个就是刚才我说的那个 Maas。

面试官
19:39
用的 seven Max 是吧？调的 API 是吧？你们这个。

刘永强
19:39
对，这个有些就是这个主要是调了 Max，然后有些小任务的也会类似到 logo，要是说一个快速问答的，那有可能调一个也比较小的模型。

面试官
19:39
嗯，行，好的，你们这个 agent，你这块你能稍微再展开介绍一下吗？

刘永强
19:39
OK，刘富 agent OS，其实我理就是类似于很多，就是我们说浏览器操作电脑操作浏览器，然后什么那个智能汽车里面的一个质量操作系统，他们其实核心，那个核心的一个逻辑核心的里面核心的一个模块就是我怎么把智能语言 quality 变成它的对应的一个 action？

刘永强
19:40
其实就是说，就是最，还说得最直白一点，就是怎么去把，怎么去调方式扣，怎么调工具后面的其他东西就是围绕这个东西提升效率，围绕调工具我们怎么去做？context in entering 怎么去做这个 lag？

刘永强
19:40
做这个记忆，然后做上下文，然后这个就是，对，就是它的逻，它的整个工程逻辑的话，就是首先有那个在机器人跟新人对话，用语音对话，然后机器人肯定会收音，收音以后会有个 ID 检测，会免唤醒，这面前面有个免唤醒，然后 VAD 检测，检测完以后是有效的，Vid 的话它就会调 SR 模块去掌握成一个 test 文本。

刘永强
19:40
test 文本以后，那就会前置的工程上有个前置逻辑和判断这个意图是什么？如果意图它是走 leg 或者走其它的，或者走一些那个假逻辑，还有一个是直接走说 legal 工具调用 function call 的话，那我们就会，就是，就会调到这个大模型，嗯，个这一块的大模型这个逻辑里面去，那这部分的话就是属于就一个大的逻辑，就是怎么计算保证说用户这 ID 出进来以后，我们给他以及识别他意图，然后识别应该有哪些工具，是走几个工具，然后提取一下参数。

刘永强
19:41
然后返回出去，然后工具非工程费用或是后置处理逻辑包括返回给机器人，然后机器人会调一个 TTS 就输入给用户。

面试官
19:41
你们这个是单轮交互的吗？就比如来一个问题，以后做完意图，然后后面提参数调工具，这是一个单轮的吗？

刘永强
19:41
多人就是有对话，历史需要有对话上下文呢。

面试官
19:41
我说的当然就是那种咱们调工具的话，其实有一些可能涉及到那一些 planning，是吧？

刘永强
19:41
这单不是多部，

面试官
19:42
本来你们对。

刘永强
19:42
是吧？现目前是单步，因为一这个是这个，因为我们响应时间是有要求的，不允许嗯，就是说一个 planning 或如果用户问了多个意图的话，我们可能会告诉用户目前是什么样的，我会告诉他，然后就是交互的话都是，嗯，不允许，用户也不可能等你说一句话跟你交互过程等10分一分钟，这个还是说是张亮裆部。

面试官
19:42
明白，等于现在只支持单意图的，是吧？那你们前面那意图的模型是什么？多大量级的模型啊？也是千门 Max 吗？

刘永强
19:42
那个是小的小模型，然后就是问，就是看它是不是需要调 lag，然后调知识库问答，或者是说那个做一个其他的简单的一些一个查询，我们公司有一些其他的逻辑。对，这个是很简单，

面试官
19:42
OK，后面那个你们是为什么要用到千万 Max？

刘永强
19:42
意图就是用的是大概是7B，也是7B，这个要因为它是实际是叠加的。

面试官
19:43
我不太清楚，你可以介绍一下吗？是你们那个场景比较复杂吗？

刘永强
19:43
对，你这个，这个是这个，这个效果是决定性，是产品是否好用的一个东西，然后当时试了每个模型都测了一遍，就 TMS 效果好一点，速度上也能跟得上。

面试官
19:43
你们是有多少种异同？

刘永强
19:43
因为我们国内只能用国内的模型。

面试官
19:43
多少种工具啊？大概。

刘永强
19:43
工具大概50个吧。

面试官
19:43
50个是吧？

刘永强
19:43
对。

面试官
19:43
50个的话相当于每次你需要把50个工具都送到模型里面，都送给他，让他做一个选择，是吧？

刘永强
19:43
这个任务可能不是，就是说虽然是50，它这个任务其实一个是说你要理解用户的意图，就是工具，它工，有时候工具很像，就是他就是工具，还有就是用户有隐含意义，你要去猜测背后用户隐含的

刘永强
19:44
打开音箱，打开什么是这种命令式的？可不就是，不是这种意图，不是做这种调用，而是可能说我今天穿真人衣服适合出去，那你就背后可能要调天气的，查天气，然后还有调一个视觉模型，这个东西是它要去，这个是要因为用户不会说是一很明显地进去，用户也不知道你有什么工具，用户是一个很口语化的一个表达这个地方，嗯，对，而且还是一个多轮的一个逻辑，这是第一个是工具调用。

刘永强
19:44
第二个就是说他是不是一次要调，一次要送那么多的工具？经理，这个是不需要的，这个是价值，是由我们需要工程上做一些架构，做一些逻辑的。就是我们要区分，我们要怎么去把这个也是属于 context entering 一部分，就是怎么去提高我准确率？

刘永强
19:44
我怎么去让我需要一个？我可能需要一个召回，我需要先在这个工具做个召回，还有说在场景里面，这不同场景里面我要去对这个 action 要做一个类似一个图样的东西，我有个图，一个图维护一个图，还有可能还有个别的东西，我可能需要通过一些干预的方式，我把这些，然后 action，我怎么让它精简，然后剪映到就是一个最小级，这个是一个逻辑，后面的话就是我怎么让模型理解用户背后的意思？

刘永强
19:45
第一其实满足就是让模型懂用户，并不是说其实执行用户的意图，而是要懂用户。除了难点是在这。

面试官
19:45
嗯，行，好的，了解，那你对那个多 agent 现在主流一些 多 agent 架构你了解过吗？

刘永强
19:45
do agent 架构的话，架构可能源码就是大概的一些原理型的，然后源码级别的没看，

面试官
19:45
那 open AI 他们，

刘永强
19:45
可能就是 long check 那。

面试官
19:45
对，open i 他们用的那个是他们那个 agent 是什么样一个大的一个架构呢？

刘永强
19:46
它的架构就是说首先有规划的话，我理解，我可能不一定是说得准，因为这个地方我可能得多 agent 可能研究不是很深，我首先是其实我的理解，我大概从我看的一些博客文章来看，首先是通用的话是有规划，然后对你这里你 act，然后规划完以后，然后去美务执行，执行完以后有反馈，然后反馈对这个结果进行分析，然后再去决策，再去调下一步。

刘永强
19:46
如果都是通过这种 act 方式，然后同步调最后的话，最后判断这个模，这个是不是还有最后有一个条件是结束，条件是我现在调的这几次这几步任务是不是应该结束？如果结束的话会做一个综合这个用户一个反馈给个结果，

面试官
19:46
行，好的。

刘永强
19:46
就是类似 so 里面代码编，代码模型里面 consul log cloud，基本上 cloud code cloud 基本上都是类似于这种范式。

面试官
19:47
嗯，行，好的，了解，刚才还提到你，听到你提那个 rag 那块，是吧？

面试官
19:47
RAG 这块的话，你们那个做的是。做的那个是应该是比较简单的一个 RAG，是吧？

刘永强
19:47
对，这个我们是公司是有这个组件系统，叫聚源系统，它之前很早二三年做的，其实我们就是基本上是调用它，

面试官
19:47
行，

刘永强
19:47
就是我们是告诉我们。

面试官
19:47
行，那 RAG 领域的一些策略、一些方案，这个里有了解过吗？

刘永强
19:47
策略指的是啥啊？你大概指明没？不太清楚，

面试官
19:47
就比如在做 rag，

刘永强
19:47
说的是策略。

面试官
19:47
做那个知识库问答助手之类的一些优化策略，怎么搭建那个系统架构？怎么做优化？最近你有了解过吗？

刘永强
19:48
这个倒没有太详细的要了解，可能就是，嗯，对，

面试官
19:48
行行，

刘永强
19:48
嗯。

面试官
19:48
好的，了解。时间原因咱们就不问这个项目和这些工作经历了，咱们出一道算法题，平时有刷题习惯吗？

刘永强
19:48
好。你这没抓。没事。

面试官
19:48
行，那因为我看你训代码比较多，那个应该 GKO MHA 这些应该都知道吧。

刘永强
19:48
基什么 GKO？

面试官
19:48
GQA 多投注意力机制分组的那个。

刘永强
19:48
MH 应该是。

面试官
19:48
对，那你看你能写一下那个 GQA 的代码吗？

刘永强
19:48
就是 global。这个是什么缩写？TQA？

面试官
19:49
他它 MHA 知道吧。

刘永强
19:49
知道就分组，

面试官
19:49
它是 GQ，是。

刘永强
19:49
注意力就是。

面试官
19:49
对对对，就可以是那个分组的那个。

刘永强
19:49
OK。

刘永强
19:49
这个是要是能运行吗？还是说直接说一个伪代码就行？

面试官
19:49
一个伪代码就行。

刘永强
19:49
OK，QK。对。

刘永强
19:50
就是 QKV 的话，它是它的思路，它的逻辑，它的那个原理的话，其实就是 KV 的话，它是可以，嗯，分组的，就是说在这一组里面就是怎么说？就是说嗯，q 的维度，Ka 的维度跟 q 的维度是不一，可不一样的，可以分成多组，然后这每这个组里面的话是可以共享这一个这个逻辑的，就是可假设我分五个组，然后5个组是可以共享的，这个里面的就是类似这对。

刘永强
19:50
首先我们要定一些变量，一对。

刘永强
19:51
我靠。

刘永强
19:51
希望这样子，然后。

刘永强
19:52
OK。

刘永强
19:53
这个怎么说实话？这个我想怎么。我这样没代码。

面试官
19:55
稍等一下，还没到呢。

面试官
19:56
好的，代码先不用写了，然后看呢？嗯。时间连先不用写，他看没什么问题。然后关于那个小米这块还有岗位情况，这边这块你看你有什么问题？

刘永强
19:57
我其实想知道，就是说咱们这个团队的主要工作内容这是什么？

面试官
19:57
嗯，

刘永强
19:57
然后等于6，

面试官
19:57
好的。

刘永强
19:57
咱们工作团队里面大概多少人？然后怎么分工？大概是什么分工？我对。

面试官
19:57
好的，我给你简单介绍一下，我们这边是小米集团信息技术部的 AI 应用中心，然后我们这边两个主要是面向集团信息技术部做一些大模型落地的一些工作，主要分为两大方向，一个是那个什么呀？

面试官
19:57
因为小米是一个做硬件的公司，纯生态链零售价的一个公司，所以我们需要面向这个体系两个方向，一个是那种消服方向，另外一个方向是延展供方向，延展正常共方向，因为是小米，他是做硬件，他是背后是需要一个强大的硬盘控体系来做支撑的，就涉及到那种生产研发、制造，然后后面是消服整个体系，然后面我面向是延展供体系，然后具体做的内容的话其实就是和大家应用大模型的技术都差不太多。

面试官
19:58
分几种形式，一种是用大模型加那个做知识增强，做一些问答助手，因为严查供给系这个偏面向这个硬件领域，严查供给系它是有非常多处于知识的，我们需要面向这个体系的一些用户做一些知识问答助手类的一些工具，然后另外一类是我们这个体系，因为它体系它比起传统的一些那种消服体系，还有一些互联网体系，它可能场景业务比较复杂，因为是涉及到生产、研发、制造。

面试官
19:58
然后那个整个供应，它下面有很多很多的分支，然后有很多很多的那种数据模型，还有一些细分的 engine 的，然后我们这边需要做一些多 agent 协作的一些架构来解决一些实际的一些复杂的一些业务问题啊。

面试官
19:58
这是第二类，然后就因为这是一个垂域领域，然后垂域领域的话相当于我们肯定是通用模型，可能它能力有限，然后我们就需要用一些模型来做一些微调，做一些强化学习之类的一些东西来适配一下这个垂类领域啊。

面试官
19:59
整体是分为这三类，然后我们分工的话其实也是按照方向来分的，有些人可能就是侧重于，比如那个 rag 方向，有些人侧重于 agent 方向，然后有些人侧重于模型微调或者是后训练方向，但是整体我们是按照项目来的，就是每个人可能有不同的一些侧重点。

面试官
19:59
主要就是这些，然后你看你还有其他问题吗？

刘永强
19:59
就是，OK，然后咱们是有强化学习的，有用，强化学习在做这种对齐，是吧？目前。

面试官
19:59
对对，我们最近正在做那个 RL 这块做微调。

刘永强
19:59
OK，

面试官
19:59
嗯。

刘永强
19:59
然后就是这个就是人，就是每个人都是按照项目，那就是对某一个项目，不对某个效果负责，还是说我们这一个，这针对这一个任务，然后每个人去进行协作，还是怎么个弄法？就是说就假如说我要提升，就是说在这个问答效果方面，就是可能是这两个人就全程库的这两个东西，还是说我每个人去说你库的微调，那个库的强化学习，然后那个固则一个 leg，这个是哪种分工逻辑？

面试官
20:00
大家每个我们这边项目会非常多，每个人他可能会在不同的项目中，可能每个项目里面可能分几个人，但是大家侧重点可能不太一样。比如有些项目的话，可能就是它主要用的话是靠模型的一些能力，可能这块就是负责模型的那个人主要负责有些项目问拿助手支持问答助手，他可能主要侧重于 Rag 那块能力，这块可能就不用由主要负责 RAG 那块人来负责，但是那块他是也是会用到模型一些，也涉及到一些微调。

面试官
20:00
对，但是每一个项目他可能侧重点不太一样。

刘永强
20:01
OK。

面试官
20:01
我们这块希望就是每个人可能有自己的一个，一定要有自己一个侧重点，不要是相当于大家都做差不多的东西，

刘永强
20:01
OK，OK，

面试官
20:01
做得都不深入，嗯。

刘永强
20:01
那咱们这边是，就是包括工程这边也是一起做的维护的吗？还是什么？

面试官
20:01
对对对，公正也是大家一起实践。

刘永强
20:01
OK，了解，然后咱们这个团队里面如果去的话，咱们这组里面大概多少人？就是刚刚说那几部分的人呢。

面试官
20:01
我给您说的多少人，

刘永强
20:01
对，大概多少个人？

面试官
20:01
是吧？组里目前是有五六个人了，我们 AI 运用中心整体是就是小100人，然后目前还在持续扩招，但是 AI 业务中心大部分都是工程算法，可能会比较少一点，然后我们这个组的话目前是五六个人还在持续扩招。

刘永强
20:01
然后。

刘永强
20:01
OK，然后咱们微调的话其实也用小模型，是吧？大概可能体币的 b 或几 b 的模型。

面试官
20:01
对，所有模型都会用。

刘永强
20:02
OK，

面试官
20:02
就看每个模型效果好。

刘永强
20:02
理解。就是咱们这边的话其实还主要集中在微调这一块，然后造数据微调，然后分析 badcase，然后类似于说类似做一些其实我们自己自研的，或是说也不一定要自研，或有那种说自己争做一些增量预训练或是其他的这个地方会有其他的一些业务。

面试官
20:02
CPD 这些可能在后期可能会涉及，因为在前期的话可能不太涉及，因为对我们这整体也是在起步阶段，

刘永强
20:02
好。

面试官
20:02
你在后期的话你要做深入的话，因为你像那个硬光领域，是吧？硬件功能是领域，它是有很多垂域知识的，后期我们积累比较多数据，以后可能会做 CPT 这些。

刘永强
20:02
OK，了解了解。诶，但我还想问一点，就是就是这个哈，就是咱们这边的话，这个就是类似于因为有些效果的话，其实我们很难去评估，就是类似，因为就是这种大模型落地的问题嘛。

刘永强
20:03
就是你可能微调之后就分析 badcase，一个是说我们去 case by case 去解决，然后还有说我们效果的话，可能有的时候我会调的话，就是可能因为我们这种用户关注的是端到端的效果，然后但是我们肯定会很多方向分 leg，然后分这些东西，因为我们现在，其实我现在其实也在做这个，其实我们会遇到一个问题，就是说这个东西怎么去说？

刘永强
20:03
因为很多东西其实你自己做的话只是每个人做，只是看局部。但是你要是从一个去优化的话，可能我们就从价格方面或者从其他方面去优化，还有一点的话，就是这个怎么去评估说这个每个人的 KPI 或者一个落地的一个能力，或者这个东西这个怎么去？

刘永强
20:03
怎么去？您大概讲讲，因为我觉得说到因为我理解的话，我觉得理解的大模型里面可能这个落地可能是怎么保证它效果？这个是一个，我就是关键的就类似于做一个产品，做类似这种大模型技术的。

面试官
20:03
保证效果的话，我们这边是和具体的业务团队来合作，来做大模型落地一个场景，然后我们一个项目在最开始做的时候可能会有一些多，大家可能对的可能会比较多，有一些 case by case 解决的一些场景，但是我们会在项目初期、立项初期我们就会把所有的这个业务方向的一个评测体系给构建起来。

面试官
20:04
然后基于这个评测体系，然后我们会评测我们的模型，评测我们整个架构，是吧？来做一个后续调优方向的一个指导，然后包括我们也会让我们那个业务方，我们的一些用户来参与到我们这个过程中。

面试官
20:04
然后这是这块。然后还有您刚才说的比较多，还有哪些？

刘永强
20:04
就是就是，怎么去？嗯，这个团队里面怎么考核这个 KPI？

面试官
20:04
团队 KPI 的话，我觉得我们这边 KPI 就是看你产出就是，对，因为大家都是跟着项目走的，

刘永强
20:04
OK。

面试官
20:04
是吧？跟着项目走的话，一方面是看分几部分，一方面是你的那个项目方面的一些东西，另外一方面是跟你个人技术方面的一些积累，

刘永强
20:05
OK，

面试官
20:05
因为要做算法吗？

刘永强
20:05
行，清楚了。

面试官
20:05
肯定是有，对技术方面咱们可能是需要持续跟踪一些最新的技术，然后可能也会涉及到一些模型微调，包括一些架构搭建，各方面可能都会有。

刘永强
20:05
OK，了解，好，我这边现在基本上清楚了那个，嗯。

面试官
20:05
行，好的，那咱们先到这。

刘永强
20:05
诶，好好，谢谢。