[[{"url": "https://xiaomi.f.mioffice.cn/docx/doxk4z0vaGgbAswshm1nseVbINc", "name": "Daily", "fileType": "feishu-docx", "content": "# Daily\n\n## 20250802（周六）\n\n- [#DSTE](https://xiaomi.f.mioffice.cn/wiki/CH2jwqVgniuHAak7cmjkNDGN48c)\n\n  - [手机部战略研讨会](https://xiaomi.f.mioffice.cn/wiki/Q39lwxOXeiUCUokAvTKkQRPM4ge)\n- [#IPD 规划](https://xiaomi.f.mioffice.cn/wiki/G6k2wSK35iIrlgkcAuFk3uho4fg) 确认 BOM 方案\n\n  - 定义了单据应该如何融入\n  - 自研 BOM 的方案怎么定：因为是使用规则、物料、产品结构是相互关联的，所以作业界面采用产品结构拍平 > 使用规则 > 物料的方式\n\n## 20250801（周五）\n\n- [#IPD 规划](https://xiaomi.f.mioffice.cn/wiki/G6k2wSK35iIrlgkcAuFk3uho4fg) 和毛老师单聊\n\n  - 把问题库和数据标准，完美咬合\n- ODC 访谈\n\n  - 负责难度：中等偏下\n  - 工作压力：正常\n  - 基础素质：尚可，沟通表达\n  - 没有特别强烈的转正和晋升诉求\n- #研产供周会\n\n  - 3 个牵引的项目要盘清楚工作范围，阶段性有产出和汇报\n  - 严控需求，3/4，重点项目的需求清单，每双周发我确认\n  - 体验检查\n\n## 20250731（周四）\n\n- [#IPD 规划](https://xiaomi.f.mioffice.cn/wiki/G6k2wSK35iIrlgkcAuFk3uho4fg) 测试部访谈\n\n  - [产品数据组讨论-到 930 希望达到的目标](https://xiaomi.f.mioffice.cn/docx/doxk4SMl2moGyDPf63LHaGiusog)\n  - 💡 大数据量也是大知识量，导入知识库\n    - 测试用例 33 万\n    - 代码\n- #ISC 周会\n\n  - 💡 需求计划的周报，作为做“分析”的标的\n- [#ISC 控制塔](https://xiaomi.f.mioffice.cn/wiki/TxfGwNwDvih97lkVC13kNUpw42f) 讨论\n\n  - 场景驱动\n    - 驾驶舱：BI/预警，小兵总\n    - 问答：查数/分析，全员\n    - 模拟：面向一线提效\n      - 融入到计划日历\n- [2025 H1 全员会 - 研产供](https://xiaomi.f.mioffice.cn/wiki/Y0dpw8XuGibaDXkbBYPkO6br4Wf)\n\n  - 成本用起来 作为第三个专项；返利治理，多业务线，采购金额看板\n\n## 20250730（周三）\n\n- [25Q2 战略专项 / 组织任务书 QBR｜ 集团信息技术部](https://xiaomi.f.mioffice.cn/docx/doxk4KraCyejTEopNLAeREPpuLd) 九爷：\n\n  - 原来哪些流程没有的/管得很乱，梳理清楚了\n  - 做了效果是什么，减少了多少人，剩了多少人\n  - 信息化领导小组：先抽象一层，经营管理的问题是共通的\n    - 业务有什么挑战和问题\n    - 考核一号位数字化部分\n  - 顶层设计，业务一号位要出一半\n- #ISC 多业务线\n\n  - 交付：全 O、AVAP 有问题\n    - 可穿戴的 ODM 料是重灾区\n  - 成本：严重缺失\n  - 流程管理部\n    - 规划前置、规则明确；三年可视、一年可控\n  - 复盘\n    - 歌尔的全 OMRP 的使用情况\n    - 电视的双周需求不变\n- #研产供难点会：待办\n- [#IPD 规划](https://xiaomi.f.mioffice.cn/wiki/G6k2wSK35iIrlgkcAuFk3uho4fg)碰头会\n\n  - 未来一个月：P2、P3、Q200 的复盘引入业务问题\n  - 产品竞争力的指标\n    - 用户构建吸引、NPS、特性 NSS\n  - 谈了需求，就需要谈资源\n\n## 20250729（周二）\n\n- 准备 [25Q2 战略专项 / 组织任务书 QBR｜ 集团信息技术部](https://xiaomi.f.mioffice.cn/docx/doxk4KraCyejTEopNLAeREPpuLd)\n\n  - 一个计划控制工厂，供应商和仓库。 过去主做的是控制，今天用计划赋能\n  - 做的不好的用，空调\n- [#IPD 规划](https://xiaomi.f.mioffice.cn/wiki/G6k2wSK35iIrlgkcAuFk3uho4fg)：分享需求管理\n\n  - [整机产品开发流程 +P3 需求管理 讨论会议纪要](https://xiaomi.f.mioffice.cn/wiki/VoqlwA5CIiIrDAkf4o2kKIAi4Og)\n  - 💡 产品数据：可以共创下目标、落地策略，以及各自讲下产品数据的范围，痛点\n\n## 20250728（周一）\n\n- [2025 H1 全员会 - 研产供](https://xiaomi.f.mioffice.cn/wiki/Y0dpw8XuGibaDXkbBYPkO6br4Wf)\n  - 反思\n    到处撒胡椒面，越做越多，越做越散\n    系统性思考，抓主要矛盾\n\n认知不深，不能共识价值\n\n不能共识价值，不敢投入\n\n不敢投入，导致认知不深\n\n- [手机部例会重点内容](https://xiaomi.f.mioffice.cn/wiki/NXDcwBAjiijg9sk7qYpktbVb4Ic)周例会以及经分会\n  卢总总结：\n  中国区高开低走，17% 16%，目前 Q3 15%，全年保 17+% 的市场份额，8 月份的 Note，9 月份数字，至关重要，Flip 和 K 至尊输了\n  P16U 搞明白，到底是客观指标有没有变好，主观指标能不能变好，O16U 最后一个月紧急公关，一塌糊涂。能不能不要换处理器。中端机应该是对于高端的复用，不应该占用太多精力。是不是平台化，模块化做的不好，把根因搞明白。\n  P2、P3 的问题仍然非常多，需要评估下， 哪些问题上市前能够解，哪些上市仍然有风险。\n  去年的国际 Note 输了，导致少了 30 亿。\n\n  高端研讨会，聚焦到超高端怎么突破，汽车高端的成功有很好的方法论。用 4000 的惯性做 6000 以上，不可行。这引发了 O8 失败的反思，比上一代掉了 50%。战略统一输出思考，改变了很多想法。有一点，越往上，用户对体验的要求越来越高，到了 6000+ 对体验的标准是 95，他对你的高线的要求非常高，95% 的存量用户在华为和苹果手里。他对你手机整体的要求是一大块，这也是 OVRH 的没有突破的原因。这就是折叠机 60% 在华为的原因，我们一直在亏，用户还不来。\n\n  底线还是基本功，要做稳定的交付，高线的要求，还是技术积累。\n  外屏如果没有真正的价值，8 系列已经讲过，用户不会买单。\n  P 代是所有升级的大年，Q 代所有产品拿出来重新过一遍，如果是小迭代一定不行。\n  星期五、星期六两天的战略研讨。\n\n---\n\n## 20250727（周日）\n\n- [#DSTE](https://xiaomi.f.mioffice.cn/wiki/CH2jwqVgniuHAak7cmjkNDGN48c)\n\n  - 💡 如果真的能够理解，一家大型公司的产品定义是怎么变革的，还是挺激动人心的\n- 下周重点\n\n  - IPD 的组织和里程碑要定下来\n  - ISC 目前仍不算清楚\n  - 明确四大专项 Q3 的目标：IPD 很清楚，AI 也算清楚，大家电（闭环），ISC 不清楚\n  - 绩效要处理下，oneone 完成\n  - 紧急问题：\n    - 处理多业务线的问题，以及数据治理的课题下·\n- Jeffry 的电话访谈\n\n  - 💡 产品化运作，我们现在是按照项目运作\n\n## 20250726（周六）\n\n[#AI 专项](https://xiaomi.f.mioffice.cn/wiki/INUewxNDXiZgEhkHdN5kmFO44ul)\n\n- [问题闭环](https://xiaomi.f.mioffice.cn/wiki/JgnOwseseicfrWkDttPkOA8d4dg)\n\n## 20250725（周五）\n\n- [DSTE](https://xiaomi.f.mioffice.cn/wiki/CH2jwqVgniuHAak7cmjkNDGN48c)\n  - 三星：一个工厂，把整个团队打包出去，帮他看下情况\n  - ISC：大家电生态链 > 降本 > 智能运营\n  - IPD：端到端需求，已经包含了 前端的需求分析\n\n## 20250724（周四）\n\n- [#DSTE](https://xiaomi.f.mioffice.cn/wiki/CH2jwqVgniuHAak7cmjkNDGN48c) 务虚会\n\n  - ISC 2021 的变革目标：降 100 亿库存，成本和定价，交付稳定性\n  - 联系少，说不上话\n    - 本质是调研少，理解浅\n  - 越是没进展，越要汇报 [产品方法论](https://xiaomi.f.mioffice.cn/wiki/VQoUwf70yi0X0tkW6e1kZdJ24Jc)\n- [#IPD 规划](https://xiaomi.f.mioffice.cn/wiki/G6k2wSK35iIrlgkcAuFk3uho4fg) 正式启动会\n\n  - 发言\n    - 数据是所有的信息化，以及变革的底层，背后。\n    - 从过去的经验， 信息化系统建设有周期，数据治理是长期的运营\n      因此：\n      - 首先，需要先**定义主数据**，围绕主数据构建数据流通链路。 [2025/6/3_研产供主数据梳理](https://xiaomi.f.mioffice.cn/sheets/shtk4XJrmwfdHGy4JDyp1hhHkxg?disposable_login_token=eyJwa2dfYnJhbmQiOiJmZWlzaHUiLCJ1bml0Ijoia2E0bGFyayIsInB3ZF9sZXNzX2xvZ2luX2F1dGgiOiIxIiwidmVyc2lvbiI6InYzIiwiZGV2aWNlX2xvZ2luX2lkIjoiNzIwNDY0MTI2ODA0NTk3MTU2NCIsInRpbWVzdGFtcCI6MTc0OTc3OTU4NSwidXNlcl9pZCI6IjY3ODk5MTA3Njk1NjA0NTMxMjEiLCJ0ZW5hbnRfYnJhbmQiOiJmZWlzaHUifQ==.5821b3fab6152c0f03d78e49a77cbda214af6953176ccbd64767c0a79a4ea218&from=ipad_browser)\n      - 其次，定义 IPD 体系中涉及的**数据闭环**\n      - 最后，基于以上数据闭环共识，**推进统一工作台****，**把 6 大关键角色拉进同一工作框架，形成日常工作协同。\n  - 九爷\n    - 这次不是信息化的项目，矩阵管理是逃不开的。\n    - 苹果和特斯拉的职能，单一产品；吉利，只适合成熟领域。矩阵\n    - 横向的线，还是做二传手为主，专业技术部门\n  - 卢总\n    - IPD 是小米最重要的流程，产品的流程做不好，其他都是假的\n    - 19 年实施在现在，主线是可以的，延伸了很多子流程，手机部有编程能力，来源不一\n    - 所造成的问题是，流程不统一、数据不统一\n      - TR 评审是线下评审，很难保证数据的真实性\n      - 能不能聚焦信息化\n    - 1 个多月让信息部整合了流程\n      - 在产品开发流程做完，商业成功\n      - 这么多流程、数据，拼接和打通，还是下定决心\n        - 下定决心就是做一套系统\n      - 即使信息化项目也不是\n    - 把我们这么多年的 67 系统，整合成一个大流程和大系统，形成一套数据，真实不受人为干预的。\n      - 把整个产品的开发\n      - 产品、技术、OS 进行耦合，OS 的规划\n      - OS3，整体的开发只给了 1.5 个月\n        - 为了 TR 过不去，规划太后置\n      - 项目管理的问题是散落，很难成为项目的守护者，很难\n        - 重要的是要把项目管理拉高，手机部的项目管理部门，人员的专业性\n      - 所有的流程、组织变革，最后是业务变革\n      - 信息化是\n      - ISC 铁三角\n\n## 20250723（周三）\n\n- [#AI 专项](https://xiaomi.f.mioffice.cn/wiki/INUewxNDXiZgEhkHdN5kmFO44ul) 灵感\n\n  - 虽然不做联网，但是可以联网高质量的书拿进来（直接去搜集红宝书，或者找高质量的文章）\n  - 知识问答\n    - 把 Agent 引进来，Agent 有更深入的知识\n    - 低质量问题 →  被抛出来 →  解决\n- [#DSTE](https://xiaomi.f.mioffice.cn/wiki/CH2jwqVgniuHAak7cmjkNDGN48c) 后续重要方向\n\n  - 战略上，ISC 就是做计划和按计划执行，控制塔解决计划质量问题，剩下的就是厂和商的管理和赋能\n    - 计划做准\n    - 先管住再赋能厂和商\n    - 生态链和大家电变革，搞清楚业务价值\n  - 概预核决\n  - IPD 需求管理，项目管理，TR 管理，DCP 管理，知识管理\n- [#ISC 控制塔](https://xiaomi.f.mioffice.cn/wiki/TxfGwNwDvih97lkVC13kNUpw42f) ISC 闭环\n\n  - 计划越做越好\n    - 预测和模拟越做准\n  - 知识让问答强大，什么让算法更强大\n\n---\n\n## 20250722（周二）\n\n- [#IPD 规划](https://xiaomi.f.mioffice.cn/wiki/G6k2wSK35iIrlgkcAuFk3uho4fg) 准备 PPT\n\n  - 找卢总关于数字化是抓手\n    - [新零售实践分享](https://xiaomi.f.mioffice.cn/docs/dock4uee59k0zbS1E1xLW8w0Oih)\n  - 数字化的问题\n    - **不存在「一个 IPD 系统」**，只有“67 个 IPD 相关系统”\n      **数据未循环，没有动力维护数据**\n    - 弱标准，弱协同，系统功能有有了流程和信息化耦合度不够\n- #研产供难点会\n\n  - 控制塔的要控制住这个方向：逆向推衍下（端到端有控制哪些？）\n    - 计划、厂、仓、商\n    - 计划：从只控制备拉配的计划，延伸到主计划、MDS、MPS 等\n- #硬工 QBR\n\n  - 支持 26 年的预算\n\n## 20250721（周一）\n\n- SAP 团队合并 陈阳\n  - 2 月份和高明已经有沟通\n    - 架构\n      - 没有用到 SAP 里面的功能，稳定性和效率没有释放出来，在架构方面有缺失\n    - 工作模式有非常大的问题（产品方案已经做到 FS，没有任何技术把关）\n      - 第三周、第四周，只要评审过的，都被打回去。但是马上要升级、归档。\n      - 技术委的单测要求，没有任何实际用途\n    - 解法\n      - 只能 Push 研发往前站，顺应标准程序的行为\n  - 你理解今天面临的挑战？短期目标？\n    --------------------------------\n  - 收益\n    ----\n  - 关键负责人：高明\n\n---\n\n## 20250720（周日）\n\n- [#AI 专项](https://xiaomi.f.mioffice.cn/wiki/INUewxNDXiZgEhkHdN5kmFO44ul)\n  - 其实推荐只需要把“点赞”做好就行，订阅是订阅在某个分类下的新内容，某个角色他发了新的信息。\n  - 新人场景\n    - 新人飞书 @ 导师“提问”  → 等待导师回复信息 → 导师回复信息 → end\n    - （米小研 push）新人 @ 虚拟导师“提问” → 立刻得到答复 →  如果未命中有效答案 →  手动给导师求助  or 自动识别 → 飞书群（给导师发信息，导师回复信息 ）→  自动总结内容，回到双方的知识库（抄 flomo，双方都能编辑）\n  - 点子\n    - 让全搜支持 @，用户强感知。显示上：@ 王才【PCB 知识管理员】 @ 占利【青蓝导师】\n    - 每个新人，有三个人为他提供服务，导师、部门助理、HR、\n    - 未来：“为每个人自动填充信息，他来选择，看或者不看”\n\n## 20250719（周六）\n\n- **#AI 专项**\n\n  - [思考 AI 专项](https://xiaomi.f.mioffice.cn/wiki/CF00wWkvnihKSHkdKhQkC61A46c)\n  - 抓住问与答（系统知道找谁，建立链接）\n    - 知识管理员，青蓝导师，产品与研发，老板（新文档，新进度）与员工\n      - 老板的助理\n    - 一个产品形态：给每个角色，分配一个 Agent，就可以 @ 了\n      - 被问的人，就是另外一种形式的 Agent\n      - 卡片笔记\n    - 把 collection 升级为，空间\n    - 基于赞和新增，形成 feed 流\n    - 区分知识来源：\n- 📚 [Peter Deng 的产品心法：硅谷最被低估的华人产品大师，ChatGPT 和 Instagram 背后的推手](https://xiaomi.f.mioffice.cn/wiki/CatwwSxnnixscrksSTKkclnZ4wZ)\n\n## 20250718（周五）\n\n- - - DCP：\n    - 产品竞争力打造\n      - 需求管理\n      - 产品&技术规划&开发：TR\n      -\n    - 业务规划\n      - 需求管理\n      - 产品&技术规划&开发：TR\n      - DCP：\n      - 各功能领域建设和知识\n    - 半年：\n      - 需求管理\n      - 预研、开发、产品规划：\n      - 产品规划、技术规划\n      -\n- - - TR 怎么度量的问题\n      ----------------\n- - - 如何让一支团队快速有战斗力\n      - 明确的目标\n      - 形成反馈闭环\n    - 产品设计上，先僵化再优化，问为什么不能复用\n- - - 总结：三套体系，1 个检查\n      - DCP\n      - 需求（卖点），TR（质量），项目管理\n    - 需求管理对竞争力负责，TR 对质量负责\n    - O12 全靠筛选出货\n- - - 软件 15 个月、硬件 18 个月\n      - 产品：\n      - 芯片：\n      - OS：\n      - 预研\n    - 软硬以 TMG 运作\n    - 要以“技术预研平台”，定位是技术货架\n    - 案例：\n      - O12：K80 为什么选了有竞争力的马达，但是在摄像头不投钱\n      - O88：\n    - CBG 调性评审\n    - 从外部看价值是什么？Quickwin 的项目选什么？\n    - 交付过程不怎么协同，打造产品竞争力没有说明\n    - 软件：1 IR ：4 SR，SR = AR\n\n## 20250717（周四）\n\n- 高阶中阶评审\n  - 供应商协同：\n  - ！数据分析，\n  - 张明慧：做的太小了，拓展视野\n  - 灵感* 至少想两步、甚至三步，回复时间点问题\n    - 基础工作，应该让校招生承接，用于练兵\n\n## 20250716（周三）\n\n- **#AI 专项**\n\n  - 发挥精准搜索的优势，接下来的应用应该是个人的知识问答 ➕ 订阅（参照 imma）\n  - 先跑一份大模型的答案，和知识库混在一起\n- **#IPD 规划**\n\n  - 项目管理和需求管理\n    - 小雷：需求如何和技术预研耦合\n    - 之前没有人真的做完整的需求定义，各个领域做完之后\n      - TMG 主要做的天线、软件的交付\n    - 联合项目管理，\n    - 汽车的 IPD 暂时进不去，但是研究下 IPD\n    - 💡 核心还是沿着需求管理，把所有任务拆明白\n  - TR 评审\n    - 华为 XR 和 TR 分开\n    - 华为消费者  TR →  TFAR\n    - 华为调性：\n      - 雷总例子：汽车 200 个颜色，去做调研；但是手机部只给了 1 个颜色\n    - 两个具体例子：\n      - 小折叠 FFR 预测\n      - 丝印出错，导致 P15A 交付不了\n    - 💡 希望把丝印打造成一个 Quickwin\n  - 参加软件部的赛道评审会\n    - [软件部流程学习](https://xiaomi.f.mioffice.cn/docx/doxk43n7vzxApfAnIoLFEc7kiBe)\n  - 尝试整理白皮书：[IPD 数字化白皮书进展](https://xiaomi.f.mioffice.cn/docx/doxk4XnYHAGbPiRObmotOzgf8zf)\n    - 问题 1：业务痛点和问题归纳，要与信息化 数字化匹配上，与数字化发展阶段匹配，总结出信息化是唯一出路\n    - 问题 2：更新业务架构图\n- **#其它**\n\n  - 总结会材料，安排黄凯更新了部分\n- 目标：\n\n  - 明天主攻白皮书和 QBR 和领导小组例会\n\n## 20250715（周二）\n\n- **#AI 专项 **\n\n  - 针对 知识不足的情况 回答效果不好？\n  - 和晓峰共识：严谨模式和通用模式没法混用，只能二选一\n- **#IPD 规划** 唐老师召开共创会\n\n  - 需要论证：信息化是唯一出路，信息化是 1.5\n- **#大家电** QBR\n\n  - 🤔 所有汇报的通病，不喜欢暴露问题\n  - 现场发言：\n    - 推进的优先级是，BOM 优化、项目管理（包括任务、人效和指标）、问题管理。\n    - TR 和需求管理是属于业务要先变革的，所以我们本不抱预期，想着先把理念传递到，事实上也确实如此，他们业务方案迟迟未出\n    - 设计工具属于我们很谨慎，但是业务诉求越理越多，本质上设计工具提效带来的业务价值并没有清晰讨论出来。\n  - M5\n    - 不喜欢 VMI，供应商在我们附近做什么仓库\n    - 园区内的管理\n    - 2 周没有承诺所有大货，应对市场变化的 2 周\n  - 代工厂 930 全部转绿，M5 1230  全部转绿\n    - 冲刺计划制定好\n    - Mark 招了人\n  - 分享\n    - ISC、智能制造、精益\n    - ISC，类似 MBS 是交付，供应链的效率，达成关键的交付指标\n    - 每个业务达成\n  - ISC\n    - 业务在提速，23 年提的是 30 1000 亿，28 年到 1000 亿；\n      - 九爷跑了一趟，可视度非常高；\n      - 今年 300 亿；23 年 9 月份，100 亿\n      - 未来每年的量级都很大，订单管理没法管，mark 也没法管，几百亿的采购额\n    - 工厂开始引入，mark 定规矩，入门的门槛，你要进门，就需要接入，接入满了\n  - IPD\n    - 预研一代\n  - Lims\n    - 不仅是我们自己来做，也能赋能给供应商\n  - 大家电出海\n    - 欧洲不能超过 3 次，要在系统上修 3 次。不能进入多次维修的状态。\n    - 服务的分段评价，全屋智能也是多次评价。\n    - 出海的超预期，海外设备的良率会比国内的好。\n  - 介绍了“盘古”，对应大家电\n    - 固参分离、模块化；和 IOT 平台里面支撑不了这个问题，也支撑了。\n  - 大的工具都是走集团，\n    - 实验室管理的系统，工厂的物流的系统，要考虑赋能给供应商\n    - 大家电比中国区的 BI 好用的多\n  - 为什么一定要对代工厂赋能？\n    - 未来三年是发展最快的三年，不够强悍\n    - 到今年年底 1500 人，对内把效率提起来，对外产业能力提升的，大家的能力都很弱，整体的能力都很弱\n    - 长虹工厂干不过，美的的自研工厂，做提升的计划，费用也不需要待\n- 单总\n\n  - 收集所有人的需求，形成一套方案。我们有很多工具，打补丁大不了。也欢迎反向输入\n  - 后台的规划逐步清晰；\n    - 有几个往前去走的事，**爆单的模型**有没有机会能做完它，做所有分货没有逻辑，很多都是 10 天了。每个工厂的，做的非常好。重要研究的重点。\n    - 有 1000 万空调联网，那为什么不能激活来整理\n  - 全屋智能，选型选装挺搓的，纯粹外包。简化销售终端的卖货，小米商城天使单 40%。\n    - 提的是第二个是，软件升级的方向，解决我们做的不好的地方，功能和需求，服务器都没有，仿真 50 个人就卡主了。\n    - 支撑全屋支撑的发展，提到比较高的高度，至少有项目和产品经理。\n\n## 20250714（周一）\n\n- **#AI 专项**\n\n  - 💡 每个小时订阅一次，基于我的日程，群聊，手动记录的信息，生成一张卡片。\n    - 然后卡片还支持二次编辑\n    - 呼应了小雷的诉求，生成的内容能够二次编辑\n- **#IPD 规划**\n\n  - 💡 飞机上，读服务白皮书有感，应该再重新阅读访谈纪要，去理解\n    - 服务\n      - 在剖析以用户为中心，业务逻辑的问题\n      - 对根因的剖析：分类服务，以商管商\n    - 手机\n      - 纲领：“技术投入不足”，“产品规划不足”，“过程质量不足”，“基建与效率”\n      - 举措：对于业务变革，TMG，产品铁三角，研发效率与基建，全面质量管理，统一数据架构\n  - 郭瑛\n    - 孙伟：把物料，生产管住，就是把研发质量\n- **#大家电 **\n\n  - BOM 汇报，分工厂认证被拍回来了？\n    - 产业上下游，就牛逼了，为什么？\n    - 没有 ISC，30 40 个人管不了供应链\n\n## 20250713（周日）\n\n- #AI 专项\n  - 目标：精准搜索\n    - 策略：\n      - 把模糊的词语变得准确，比如“P2”→“P2 项目”  or “P2 阶段”\n      - 圈定明确的上下文，比如“某个期刊”，“某个时间段”，“某几篇具体的文档”\n    - 具体场景：\n      - #1 P2 项目的 PCB 相关有哪些问题，您帮我生成一份周报 →  准确理解：筛选条件是项目=#P2 和领域=#PCB 和类型=#问题*，#周报*\n      - #2 总结我这一周的周报 → 准确理解：人=陈晓东 和 时间=#近一周 和 类型=#周报*\n      - #3 昨天 P2 项目有什么动态 →  准确理解：时间=昨天 和 #P2 和 类型 = （包括日报、问题、文档）等\n      - #4 总结这几篇文档的核心观点 →  手动上传文档\n  - 方案\n    - #1：大搜逻辑，直接根据 query 进行搜索，然后手动选对象、添加规则\n      - 大搜逻辑：分词之后，检索不同的数据源，然后重排序\n      - 优势：搜索\n      - 劣势：路径太长，预览 → 添加 ，用户可能会困扰，到底要搜索\n    - #2：从 query 中，提取标签和类型的组合\n    - #3：把 query 替换为标签和类型\n      - P2 项目的 PCB 相关有哪些问题 →  #P2 项目的 #PCB 有哪些 #问题*\n  - 订阅：把确定性的先\n    - 科技圈动态\n    - 项目日报\n    - 会议纪要（含文档、评审）\n\n## 20250712（周六）\n\n- 面试 2\n\n  - 先代码 → 需求 → 自动化测试 → 度量 → webIDE\n  - 数字化底座，防止重复建设\n- 需求管理讨论\n\n  - 小雷\n    - 需求管理范围：软、硬、预研，贯穿协同\n- [IPD 信息化白皮书（草稿）](https://xiaomi.f.mioffice.cn/docx/doxk4xDoZLnOisxuKsrODO5bdbh)\n\n## 20250710（周五）\n\n- 周会总结\n\n  - IPD 统一工作台、供应链控制塔、大家电、问题库 xAI\n  - 明确人员名单和里程碑，进入战时状态\n  - 进入战时状态\n- 二级料\n\n  - 二三级物料进行品类划分，交期从线下转为线上，对线上化在交付、成本优化\n    - 包括招标优化\n  -\n- 供应链控制塔\n\n  - 世界最好的模块和标准\n- AI 专项\n\n  - 高级搜索 > 自动补全\n  - 高级搜索要人性化，参考抖音\n  - 高级搜索最好和\n\n## 20250709（周四）\n\n- 集团\n\n  - 从机会到能力建设，核心是组织力建设，最重要的 400 个核心干部\n    - 提高视野、胸怀。只有看见，才有机会做到\n    - 系统化的思考。不能把问题搞清楚，搞头。\n- 埃森哲 IPD 交流\n\n  - [IPD 埃森哲交流](https://xiaomi.f.mioffice.cn/wiki/PaiPw2R6bilRdWkVK1kkb6ps4Ie)\n- 埃森哲 AI 交流\n\n  - [供应链控制塔调研](https://xiaomi.f.mioffice.cn/wiki/HYC0wKh6Zi24XtkRkyuk2kMg4Cg)\n- [MIT2025 年 H1 组织盘点&绩效校准-研产供数字化部](https://xiaomi.f.mioffice.cn/docx/doxk4poekp87YvpaWdJWgZDOrqf)\n\n  - 手机部发起 IPD 变革，提升到 ISC 的重要度\n  - ISC 进入数字化阶段，打造供应链控制塔\n  - ISC 多业务线建设高峰在 25 年底基本完成（未考虑生态链、大家电）\n\n## 20250709（周三）\n\n- fy 的 oneone\n\n  - 源头：没有真正经历成功和失败，“要”性不足\n    - 表现为：取舍不果断、不擅长造势、不充分用人\n  - 研产供有破局点，但要警惕因为不够聚焦延误战机\n    - 破局点：IPD、智能化、供应商赋能\n    - 比如：IPD 如果 fy 不参与，很容易发散导致停滞\n  - 如果必要，如何把三个破局点都做好\n    - 一段时间主攻一件事情，更重要需要 3 个 20 级\n    - 追问：fy 如何操盘多业务线？通过 QBR 明确聚焦到大家电\n- IT 架构\n\n  - 集团和子公司，年度会组合。\n    - 平台通用系统，运维系统由子公司来提报\n      - 平台：包括运营、数据管理、人工智能、技术架构\n      - 子公司：采集都在集团\n      - 集团 200，160；\n    - 人数怎么\n    - 需求如何\n  -\n  - 如何保障数据质量\n\n---\n\n## \n\n- 欧菲光的交流\n  - **Oracle 数据统一**\n    20 年之后，Oracle 无限制部署，全国最低 1200 万。2 年内所有的部署都是认可，现在 5000 个 license 足够支撑 100 亿。\n    20 年之前，帆软的 BI，也是用的 Oracle，数据湖可能会做新方案。\n  - **IPD**\n    IPD：by 动作看，小米 P1 对应 华为 SV1\n    目标会做分解，fema，规格对标，器件和模组规格，拿一个项目方法。后面每个月看进度，\n    管控的方法一致\n    华为的管控更细，除了结果，还会看到过程。过点大家一起过点，过不了的时候，2012 平台会过来 10+ 博士共同攻克。\n    共同过点，会来现场进行检验。\n  - **质量管理**\n    华为：\n\n    - 要了最后一站的测试数据\n    - OTP 的数据都上传到云端，每个项目和 goden 比\n    - 19 年之前，会反馈 SPC 达标，\n      SPC 达标率\n      OQC 批退率\n      工站良率\n      OQC 达标\n  - **算法应用**\n    典型：连接器的检测，上了 AOI 之后，结果\n    厂内的大模型。\n    无代码化训练，案例：胶型检测，产线工人标注，是否有断胶。所以\n\n    AI 赋能检测，生产，\n    都是过程 AOI\n    芯片上板子 拍照\n    滤光片 检测\n    马达和镜头\n    马达的焊接\n    模组外观，6 个面自动判定\n\n    VOI 的收益评估：人和良率，效率层面\n  - 数据治理：对于数据治理的理解还不深，认知上还是 IT 和业务拆分\n\n    - 设备的标准化还没概念\n    -\n    -\n    - 流程 + 表单\n    - 问题相关表单 30 多个 vs 华为 3 个\n    - 自主开发程度比较高，还是有所差异\n    - 20 年开始做，在集团内低调在做，24 年有数字化转型方向的时候\n\n**质量管理和华为对标**\n\n- 良率、质量驻场\n- 华为的差距：\n- OPT 的数据，整机工厂运营\n-\n- 首件的数据考虑回传，由需要考虑调整：CPK 某一些尺寸 1.33 不是 1.0\n- 首量\n- NCR4、5、6 线上过节点，拉研发、品质拉齐\n\n## 20250707（周一）\n\n- **蓝思供应商拜访**\n\n  - [蓝思项目量产交付动员大会-20250707 汇总版.pptx](https://xiaomi.f.mioffice.cn/file/boxk49Jz46auAcwvn2pI5StRYkR)\n  - 分公\n    - 黄花园区：Lipo、电池盖\n    - 湘潭园区：P2/P3/Q200\n  - 交付军令状：\n    - 电池盖：60K/D → 80K/D\n    - 中框：278K，总人力 285，950 人\n    - Lipo：华兴显示屏不足，需要推动\n    - 前壳：20K/D～30K/D，协调增加产能，微晶已经全部满足\n  - 开发总结\n    - 电池盖项目开发：\n      - 有完整的项目管理，交付件约 240 项，包括：资料受控、图纸评审、能力评审、认证评审\n      - 评审有效吗？小米会抽查吗？\n    - LIPO 项目开发：\n      - 前期结构评审，参考 O3 项目经验，对 LIPO 成品胶路进行模流仿真\n      - 提效专项：1）针对自动线取消二次固化 +CNC 铣水口，成品检验硬度 + 气密测试全检改抽检 2）针对拆装漏斗耗时，优化治具 + 漏斗设计，节省工时\n      - 石墨片杂志问题暂未修复\n  - 品质保障\n  - 电池\n    - AOI 检查，7/2 首次验证 P3 成品 200PCS 良率 63.50%，漏检率 26.50%，过杀率 17%，已定位问题。\n    - 三道全检，三道抽检\n  - 中框品质保障\n    - 中框需要加速\n    - AOI 检查目标漏检率 0.6%\n  - LIPO\n    - 良率 99% 已达成\n    - 针对整个 lipo 制程 8 个关键工序，已从同质性问题，过程特性，PFMEA 三个维度，S/A/B/C 四个风险等级完成成熟度评价\n    - P2/P3 系列沿用 O3 设计和材料，且使用 O3 量产线体，整体工艺，材料，设备成熟度较高，风险较低，均为绿灯；\n    - -\n    - 生产一线员工,特别是关键岗位根据交付计划**按照 150% 进行规划配置**\n    - 从进料到出货全制程采用 MES 系统管理，可收集所有制程参数，测试数据和参数，并对制程异常品进行自动锁定\n  - 交流\n    - 除了 Deco 之外，其他二级料的交付有挑战\n    - 前盖的微晶需要小米一期催料\n    - 建议每天点检，如果没有\n- **#IPD 规划**\n\n  - 新零售的变革是提升渠道效率，线下渠道要打平线上京东渠道费率\n  - 业务模式的三大升级：\n    - 简化业务模式：一镇一店等\n    - 简化组织模式：渠道简化为米家和授权两类\n    - 算清每家门店损益，数字化穿透管理\n  - ISC 的变革：库存大幅度降低\n    - 主需求数据贯通：计划管控工厂，仓储，供应商\n    - 业务模式升级：引入央仓以及 VMI\n  -\n  - 砍渠道商本质是消除信息衰减和利益分层，那么 IPD 对应的“渠道商”是什么？显然是传统研发中那些阻断信息流动的“中间层”：冗余审批、部门壁垒、技术孤岛。\n  - 从“段到段”到“端到端”：\n    - 从专业角度，这个类比需要三层映射：第一层是现象（砍渠道商/砍决策层），第二层是手段（数字化/结构化流程），第三层是目的（提升终端响应力/加速产品上市）。\n    - IPD 的“直达战场”体现在三个致命环节：决策权下沉给 PDT 经理，需求直连客户原始声音，问题暴露在早期评审而非量产时。\n\n## 20250706（周日）\n\n- **#AI 专项 **\n  - 用户和问题是第一位的，所以还是应该回到都能查（知识上传），精准回答（数据模型），复杂回答\n\n    - 演进的方向一定是回答问题的深度\n  - 这次跑偏还是因为没重视用户需求，始终盯着：[2025/06/24_PCB 领域 query 分类](https://xiaomi.f.mioffice.cn/docx/doxk4jn3xJJYWC2xGfSnMJijkHb)\n  - 715：知识库闭环， 硬工全员宣讲推广\n\n    - 关键策略\n      - 查的准，95% 准确率，终态不能人工来选\n        - 要联想可能要问的问题\n      - 性能好，1s 内响应\n      - 不越权\n      - 验证系统数据查询：项目主数据、器件选型单，能够让晓峰全局搜索\n    - 看情况：\n      - **上传方便，3s 内完成上传，5s 审批，分钟级可用**\n  - 730：精准查询数据模型，和周报系统集成推广\n\n    - 关键策略\n      - 增加“@ ”：@xxx 说过的话；@xxx 写的文档；\n        - 终态，@ 之后的表达符合人的表述逻辑，过渡阶段不强求\n        - 通过 query 把 @ 推荐出来\n      - @ 的实现逻辑 = 信息流的逻辑，本质是创建筛选视图\n      - 挑战和周报系统集成，取决于数据质量\n        - 生成周报的 query = “ 生成我的周报 @ 我解决的问题；@ 我写的文档；@ 我参加的会”\n  - 9.15 二次传播：订阅和推荐\n\n    - 你应该阅读的信息（文档）\n\n![](boxk4Ub0u3ANHR3UDmMccbCM3qc)\n![](boxk4rvqX3Mx2e4JprtiHx9INvd)\n\n## 20250705（周六）\n\n- 把车位申请下，找下助理申请\n- 游泳（由面试总结想到的）\n\n  - 优秀的产品经理，在于用户访谈的次数，所以把所有的用户访谈，归档上传统计\n  - 把领导的发言总结，作为信息流的多一个场景。领导发言，打上标签，推送到对应的项目组\n- [IPD 重要面试记录](https://xiaomi.f.mioffice.cn/docx/doxk4wcGKMMyjvJ6bcFcvSETNyb)\n- 灵感\n\n  - 一级是在群里，随时活跃\n  - 二级是每日订阅\n  - 三级是每周订阅\n  - 四级是没双周订阅\n- 数据运营周会\n\n  - 请所有人，读完《逻辑思维五步走》\n- 周会\n\n  - [供应链 agent 项目计划书](https://xiaomi.f.mioffice.cn/docx/doxk4aRdGJd3Yu5W7n0rDfm9tHd)\n  - 高目标牵引\n  - 什么是业务变革？\n- 武汉研发\n\n  - 武汉-制造组\n  - 武汉-产品\n  - 北京-采购执行\n\n## 20250702 （周三）\n\n- 小步汇报\n\n  - 26 个模块可以让业务流闭环（72 人月），实施部署，评估下未来需要花多少时间（去掉重复投入）\n  - 快速实现，\n  - 我们用 3 个月导入？和买一套金蝶、用友会得到什么？\n    - 重点：和 ISC 体系是一套标准\n  - 把 ISC + IPD + WMS\n    - 把多哥拉上，就是一整套了\n- 大家电汇报\n\n  - 复制大家电看板到电视看板\n  - 基地仓是否需要存在？\n- 问题库 xAI 启动会\n\n  - **项目目标：7 月底，AI 问答产品全面推广工程开发部（829 人），周渗透率目标 26%（PV=5）**\n  - **产品目标：做研发领域的 perplexity**\n    - 毫秒级的搜索体验\n    - 比肩行业的大模型问答水平\n    - 海量的研发知识\n  - **对过去工作的复盘：**\n    - **高目标牵引：**对标世界级的产品（perplexity, glean）\n    - **统一思想：**共创共识，语言一致，行动一致\n    - **产品洞察：**站在用户的背后，把用户的声音转化为真实需求\n    - **坚定投入：**要么 all in，要么离开\n- 大家电\n\n  - 大宗损益经常变，考虑全年做一个定值\n    - 给到战区的内存成本已经守不住了\n    - 卢总：过程要透明，经营意识，快速上报\n    - 手机的经营没有缓冲区，业务到\n- 面试\n\n  - 目标：就是从偶然到必然\n- 总结会的灵感\n\n  - 上半年大事记\n    - IPD 共创\n    - ISC 破局\n    - 数据看板\n    - 大家电\n    - 问题库\n- 单总\n\n  - 基调：讲成果，一套 isc，在快速增长的业务下，稳住了交付\n  - 在 ISC 后面讲\n  - 其他\n    - 汽车反思，看小兵总讲\n  - 灵感：面向大家电三年\n    - 五地供应、出海\n    -\n- 多业务线难点会\n\n  - 方法论总结\n    - 信息化提供的斧头，你得挥动起来才有价值\n    - 系统使用情况 → 业务运营能力（可视、可控、更快） → 业务价值\n    - 典型案例：返利、VMI 节省库存\n\n  - [ ] 和范宇说下大家电的会怎么开\n- 问题库复盘\n\n  - 晓峰：分散信息，快速检索\n  - 占利：80%，觉得没有把问题打透，一股脑上知识不知道是否是正确的方向\n    - 深度解决一类问题\n  - 文蕾：\n    - 问人数、找文档、找会议纪要\n    - 张冰的个人知识库做好了，第一时间传上去\n  - 郭瑛\n    - 问题：Flip 的产品口碑，问题的解决进展\n      - TR 评审有没有通过，项目里程碑\n    - 知识：\n      - 把基础能力集成进来\n      - 建议推多领域\n    -\n  - 复盘\n    - 为了交付而交付，做一款自己愿意用的产品\n- 成本难点会\n\n  - 总结：怎么做数据看板，“看数逻辑”是第一位的。查账的逻辑，先看环比，再看科目，再打开看内容\n- IPD 难点会\n\n  - 对标行业，把产品做好\n  - 灵感：待办/任务宁缺毋滥，要把 R 平台打造成官方的待办\n  - 总结：什么叫做抽象，按照语义来，能够最大化减少信息冗余\n- 问题库\n\n  - 华为 W3、小米没有\n  - perplexity、glin60 亿美金\n  -\n- 把动员会开了\n- 领导小组汇报动态\n\n  - 数字化\n  - 规模化\n  - 单总讲一套 ISC\n- 答辩\n- 把小群成立\n\n  - 庆祝核心小群成立[撒花] 随时沟通最新动态 @ 所有人\n  - 昨天和唐老师的沟通要点：\n    - 白皮书的内核是业务模式变革，要找到“穿透、简化、控制”的感觉\n    - 在精准需求交付、损益管理、质量管理的场域上是值得探讨的方向\n    - 需要引入各领域的业务专家，对各自领域的业务价值进行梳理和澄清\n  - 我和郭瑛整理下我们目前的变革思考不定期发到群里，也邀请下埃森哲来介绍下\n  -\n  - 我和郭瑛持续同步相关的项目动态，\n  - 今天我们把唐老师的系统，还有适合业务侧的候选人，\n  - 庆祝新班组成立[撒花] 以后大事小事这里说 @ 所有人\n  - 各位老板、同学，大家好，后续此群作为需求分析模型核心人员沟通群；从本周起，请项目 PMO 承慧同学 @ 黄承慧 每周五下班前在群里汇报项目进展~\n-\n\n## 20250701 （周二）\n\n- ipd\n\n  - 精准控制需求\n  - 质量管控\n  - 管理层级的缩短，直接看到所有的项目\n- 汇报材料\n\n  - 数据辅助业务决策，进入数字化 1.0\n    - 信息流：交付侧主场景基本具备做完了 630\n      - 外拓供应商\n      - 区域 PSI，年底下钻到国家\n      - MPS 是有痛点的\n    - 怎么更好的把计划\n  - 成本目前没有 AI 感，\n    ------------------\n- 研发费用\n\n  - 每个科目到 SAP 的透传规则\n  - 流程找人：统一一套人岗方案\n-\n\n## 20250630 （周一）\n\n- #IPD\n\n  - 对变革思路，白皮书，启动会。\n    - 复用 ISC 的组织设计：班委，铁三角\n      - 决策小群\n    - 评审会确认下时间\n    - 白皮书和愿景\n    - 正式启动会时间\n  - 对目录设计，交付下面的缺陷和需求视图待定\n- #ISC\n\n  - 从整体出发，Q3 唐老师能够用到什么\n  - 做了一次竞品调研，让 PMO 形成归档\n- #AI\n\n  - 定位：做到全局搜索，再回答复杂问题\n  - 通过扫描全流程，识别全数据，围绕每一个场景做甚做透\n  - 内容生产，下游推动上游 > 或者对自己有利\n- #大家电\n\n  - 单总确认可参会\n- #面试\n  ![](boxk4op2Fuldjf52vbMhreartSd)\n  ![](boxk41Z15PC3IZh8VNayR5yDfSg)\n- **#周报 **\n\n  - 汇报的思路：\n  - pmo 组织要输出，不能只是收作业\n\n# \n\n## 20250629 （周日）\n\n- **📚 梁宁 领导力和组织设计 **\n\n  - 🤔\n    - 没有共识的价值 = 没有价值\n    - 抓住机会 > 做好管理\n    - 信息力是核心优势，跨部门的协同和连接（信息流的使命）\n  - [edu.mioffice.cn](https://edu.mioffice.cn/clientcn/live/12364)\n  - 任务的分类：**简单、组合、复杂（10）**\n    - 简单任务：单一目标，明确的步骤，容易解决。\n    - 组合任务：多个目标，但目标之间没有明显的冲突，可以通过线性或并行的方式解决。\n    - 复杂任务：多个目标，目标之间存在冲突，需要协调和平衡。例如，我们在处理业务时，可能会清楚自己做的是单线程任务或组合任务。但一旦涉及管理，首先遇到的一个挑战就是多目标冲突。人们常常会说“既要又要，还要，而且得要”，这正是复杂性的体现。\n      - 星链：复杂，对错分明，降低成本\n    - 难的任务：\n      - 脑机：难，对错不分明\n  - B 是 C 的 10000 倍\n  - **领导和管理：**\n    - 如果不能摆脱竞争内部竞争，再怎么精益求精也不是好领导\n    - 什么叫做烂？机会在你眼前也仍然抓不住\n      ![](boxk4ruCnpknsbzOPDGK7DAFuOg)\n  - **时代的红利：每个时代的关键词：信息力是核心优势，跨部门的协同和连接（信息流的使命）**\n    - 工业革命：效率不对称 →  互联网革命：信息不对称 →  智能革命：经验不对称\n    - 互联网革命，信息不对称（BAT 的产生）人的信息，交易的信息，其他信息\n      - 你在企业的位置取决于有哪些信息流经过你。\n      - 💡 在 A 时代的竞争力构建一个知识持续迭代的正向循环，自己头条当年构建的推荐的一样\n        ![](boxk4nEzVcdLMHBFRq0Laz7N6Od)\n  - 雷总直面用户，10 倍沟通效率；传统经销商只跟 4S 店沟通\n  - 看清楚，主导者和颠覆者，别抄错\n  - 爱你的人视你的主体性为优点简单使用你的人，视你的主体性为缺点他需要的是你的工具性和服从性\n  -\n  - 如何积累经验\n    - 观点 1：多干一点活\n    - 观点 2：对一个活，干的更透\n  - 一个价值如果没有被共识， =  没价值\n  - 💡 看过的文档都能搜索到，要和会议纪要想结合（周报/产出）。机会，自动组建的知识库\n  - 💡 项目管理的核心竞争力是系统，所以人用的越多，它的公信力就越强。这是相比于一个个人待办清单的，绝对优势。\n- 💡 来自公众号\n\n  - 我觉得现在 AI 是这样的一个使用流程：\n    「需求讲解\n    请向我提问\n    你懂我意思吧\n    不对你错了，你是不是有点傻\n    这还不明白吗？我再说一次\n    666 你真厉害」\n\n## 20250628 （周六）\n\n- #ISC\n\n  - 总结\n    - 业务的创新实践是数字化的源头\n    - 回到 ISC，ISC 更上一层楼\n    - ISC 和 IPD 协同\n  - 计划部\n    - 换成需求满足率\n    - C3F2 案例\n    - 增加数据分析岗\n  - 采购部\n    - 体检：备料使用率，PR，拉料指令\n    - 筑基：把系统数据考核起来\n    - 二级料：把产业链协同效率\n    - 让供应商梳理 ISC 流程，稳交付\n- [[0515]手机部 AI 专题研讨会纪要](https://xiaomi.f.mioffice.cn/docx/doxk43Vgwa8ybnXZUXZFyzIdvxb)\n- - 需求上线，转成文档，周二飞阅，周三难点会只讨论问题\n- 下半年大规模导\n\n## 20250627 （周五）\n\n- ou_0ee0a29e70b44cc3eca49c66e4616e82 是说 PMO 不要只收作业\n- 面试\n\n  - 不管是做流程还是做 it，跟公司的战略跟业务的痛点以及老板的诉求是关联\n- #周会\n\n  - 把数据治理\n  - Q3 OKR，进入数据驱动阶段\n  - 强化业务共识，\n  - 破局：从事实出发，访谈和数据分析\n  - [https://xiaomi.f.mioffice.cn/docx/doxk4YUDBQ11SrjGYBdh2lRvePc](https://xiaomi.f.mioffice.cn/docx/doxk4YUDBQ11SrjGYBdh2lRvePc)\n- #**汽车 ISC+ 内存/二级料/电视 MRP**\n\n  - 数据治理明确负责人，确认所有数据标准\n    - 其中，项目主数据 需要 唐总来牵引\n    - 料商主数据去控制过程\n      - 料的主数据和上下游的关系\n  - 三层：数据 → 流程 → 算法\n  - 二级料\n    - 二级料 BOM 谁来运维，否则怎么来建设\n- 生态链\n\n  - 发言\n    - 团队的产出，准确识别痛点\n    - 供应链数据基础，\n      - 质量：每一个工序\n      - 供需：\n      - 可视：\n    - 卢总开始管质量，每一个层级的终极方向是什么\n    - 给老板，做一个小范围的介绍\n    - 多考虑几类用户\n      - 老板、原版\n        ![](boxk4Nshawzz1zi1CPuzqoibrLg)\n  - ou_374c7df2191aeb6cd455801d35c8f713\n    - 中国区季节品希望拆周交付\n    - IMME 返回，接单批量返回，对生态链公司和我们是痛点\n  - ou_b55ddc1655a04f7fe351d92cd0c2f558\n    - 代表研发，把成本相关的暴露出来\n    - 质量模块，自动化监控\n  - ou_0d64756c225fd6ef0dcc8926ad0f09bb\n    - 迫使生态链公司开始管供需\n    - 每条产线，每天的状态是不一样的\n    - 小步  》\n  - 反馈\n    - 生态链常规品的成本核算\n    - 转单的稳定性，物料比较稳定，整机的需求\n  - 北极星 #day\n    - 接单反馈周期 14 天 →5 天\n\n## 20250626 （周四）\n\n- 售后\n\n  - [售后 ISC 项目计划书 V1.0](https://xiaomi.f.mioffice.cn/docx/doxk4v9eQu7MyOSfHxfM2mgIDzd)\n- 大家电\n\n  - 九爷：16-19 级是关键抓手\n  - 空调的痛点是什么，未来某一天需要界定清楚\n    - 不知道要扩充到什么程度\n    - 通配的情况下，\n  - 空调一物一码\n    - 业务就是诉求：分工厂 PO、验证、MRP\n      - 潜在：研发想通配，但是下游又分工厂管理\n    - 合码诉求：3F 一致，全局成本和库存最优\n      - 因为手机 3F 不一致，但是供应链有诉求，因此通过 PN+MPN，+YRNX 来实现\n      - 手机 3F 为什么不能一致：国内海外、一二供验证节奏不一致等\n    - 因此要看：\n      - 要看一、二供的价格差\n      - 有没有人管全局配额，如果没人管根本不需要合码\n      - 分工厂配额，就是子码的逻辑\n  - 场景和工厂的\n    - 就和 OC 一样，不用和代码，用 1 个 MPN 发一个码\n    - OC 就是 by 项目验证的，所以就是 1 对 1\n  - 全局通配是什么？\n  - 贵的东西都是拆开的，便宜的\n  -\n  - 丝印的审批流，出现一\n- [安全平台项目规划汇报](https://xiaomi.f.mioffice.cn/docx/doxk4iEGoU0iEFsQ01nVMMnkgK4)\n\n## 20250625 （周三）\n\n- 服务和研发标签统一，和孙伟汇报\n\n  - 服务痛已经有\n  - 一个单据拉一套标签：售后领域三套，硬件 300 标签；客服领域九套\n    ![](boxk4VDfm5kqW8qsdaZUvjANrmb)\n    ![](boxk4JHJ0ohR9jg7D79n21JFBAh)\n- [HR 周报](https://xiaomi.f.mioffice.cn/docx/doxk4isT6kJlhar6JU3rNlU0wgc)\n\n## 20250624 （周二）\n\n- IPD 规划\n- [ISC 导入成功标准-大家电](https://xiaomi.f.mioffice.cn/docx/doxk4KSalhrnyBq9Bibo1iNTfeg)\n\n## 20250623 （周一）\n\n- # \n\n  - 可以点，唐老师你有什么建议？\n- #信息部主管会\n\n  - [美团效率工程研究-20250523](https://xiaomi.f.mioffice.cn/docx/doxk4buMKVhDxcxplfPCQsKEByd)\n- &灵感\n\n  - 让 PMO 团队，周六把重点拆完，周一对下本周日程。\n- 超哥建议共创\n\n  - 手机把计划做准的红利\n  - 多业务线规模化红利\n- [汽车信息化三年规划-销交服-2024.9 副本](https://xiaomi.f.mioffice.cn/docx/doxk4mZKyBU0SH7AwU9Rm2kBvUg)\n\n## 20250622\n\n- **#周报**\n\n  - IPD 规划周二和卢总汇报，和各方确认工作台目录\n  - ISC“供应链控制大脑”启动调研，澄清需求以及竞品调研，加入领导小组汇报\n  - 大家电的现状复盘以及验收标准对齐，加入领导小组汇报\n  - 需要推进 问题库 xAI 的规划，明确相比“飞书问答”的核心竞争力和下一步计划\n  - 准备半年述职\n- &灵感\n\n  - 有段时间没看过，完整的 PRD 了\n  - 难点会材料的质检，还是需要的\n- 冯唐 x《正道》：管理哲学，无为、若水、无我  &学习\n\n  - 无为：你只有少做一点事，才有可能把事情做好\n    - 不是无所作为，不是“躺平”，不是站桩，不是待着不动，而是顺应规律去做事。你要去做最关鍵的、最重要的事情，不要去做那些有的没的的事，不要过度发散，过度衍生。\n  - 若水：欲望管理的度，不是说该得的不得、该要的不要。\n    - 该高调高调，该低调低调：“天与不取，反受其咎“天与不取，不吉”，如果老天给你，你怂了、不自信、不敢要，那么不仅你得不到，很可能还会反受其害。\n    - 水要滋养万物，对人来说水不好做，因为人有欲望的骨头\n      - 很多布置工作不清晰\n        - 你让人家干什么？\n        - 希望怎么干？\n        - 希望得到的结果？\n        - 希望什么时间拿？\n      - 如果别人做的跟你的想法差距很大，用正确的工具、方法再陪他再走一走，看上去慢反而是最快的路\n  - 无我：\n    - 过度亲力亲为，没有给下属成长的空间\n    - 脸皮要厚：刘邦为了赢，踹儿女下车\n    - 会分钱：成吉思汗，那一半剩下的留给团队\n- **#AI 专项**\n\n  - 和 DS 交互，获得灵感\n  - 工程师的痛点是产出解决方案，因此最重要的是“各类解决方案，案例，调试经验”  &思考\n  - ou_a13366e9aa7faea8e1af42eac40744f3：\n    - 本质：助力研发实现和解决问题\n    - 策略：深入理解没一份数据，建立知识图谱\n    - 三个阶段：方法论；分领域问答 ➕ 智能打标；融合问答\n\n## 20250621\n\n- #AI 专项\n  - 相比于 michat 或飞书问答 的增量价值\n  - 推己及人：有哪些问答？ 👉 背后是哪些知识 👉 如何被生产\n\n## 20250620\n\n- **#研产供周会**\n  -----------\n- **#IPD**\n  ----\n\n## 20250619\n\n- **#ISC 例会**\n\n  - 标准化：（把标准化率、还有小布的情况整理下），把轻量化的流程和产品，从下订单到交付的。把流程完全\n  - ou_b1cf4d4f73e1d16d272f42c36069e81d：\n    - 汽车融合和安全体系需要加入项目管理\n    - 安全体系：中长期连续性 → 供应风险（地震、合规）→ 项目风险管理（柔性项目可视化）\n    - GSC 基于多用户视角，向产品经理统筹；可视的查询，风险的管理\n- **#M+12** 库存规划 ，实现 M+6 库存计算 ou_b1cf4d4f73e1d16d272f42c36069e81d  ～ 公开\n\n  - [M+12 库存预估](https://xiaomi.f.mioffice.cn/docx/doxk431YFsUZNLjEOGxr3EXu8te)\n  - MRP 的数据治理。通过 内存 - 手机 = 1:1 识别出来，内存差距 500W\n  - 先把品类 MRP 优化报清楚，再报库存规划\n  - M+12 的需求不能直接用来计算库存，因为成本项目下“块块”需求没有评审，不能直接备下去\n  - 相比成本晚一年，把根因分析以及自动定位到人的功能\n  - 有可能材料已经到头，**有可能要成品库存**（要管的更细，资源和物控是否按策略执行，让老板放心）\n  - 筹备给小兵总的汇报，确保能够讲出来（上一轮品类被质疑），按照三个阶段：\n    - 一阶段：人工线下在管\n    - 二阶段：VMI + 战略人工决策\n    - 三阶段：把战略品类难题解决了，需求/成本/备料上线，算出 M+6，需求和供应商反馈不支持 M+12\n    - （避免：听起来 22 年已经上线，但是实际上库存管理三阶段）\n- **#全链路成本**\n\n  - #灵感 好的看板要符合看数逻辑平阳，不需要筛选器。筛选器约定于让老板自己做报告\n- #IPD 产品\n\n  - 软件和测试都有“空间”，建议仍然保留\n- #IPD 规划\n\n  - ou_45d3dd8aa4da67fd1329cfe99f63f7e4 分享[【思考】IPD 系统体检及顶层设计（初版）](https://xiaomi.f.mioffice.cn/docx/doxk4wwMNIuSomhmC1y00YAfFSd)\n  - 看变革专项：以软件为主，来看华为 4.0 以及变革专项\n  - 看数据架构：有哪些数据要素，如何连接\n  - 最好拉上测试和软件一起共创\n- 汽车难点会\n\n  - 建议 ou_b5106e88952e0bb999e4caca059a5bb0 重视到年底，思考下如何验收，后续难点会只聊该话题，要深得下去\n- 工作模式\n- 难点会\n\n  - [2025/6/19_问题库 xAI Q2 阶段性汇报](https://xiaomi.f.mioffice.cn/docx/doxk4RQXDdCArASiSohkLBxHT7f)\n\n## 20250618\n\n- #AISC\n\n  - 7 月 1 日，半年总结 ou_b1cf4d4f73e1d16d272f42c36069e81d\n    - 交付需求管理能力大幅提升\n    - 查询金额快到临界点，提前储备\n  - #采购金额\n    - 需要聚合，高通集团有 3 家公司，按照产品线归类项目\n    - 有 rebate，现在下单是多少，采购金额是多少\n    - 建议 ou_bd4e1c523295163fd6b84fe1fd2cb131 和业务讨论看数逻辑，形成管理视角\n      - 对产品的期待\n    - 两条并行：数据治理（看板）和机器人并行走\n    - 机器人向着“可视/可预警”演进和处理，比如“新品交付”有感觉，DCP 节点全部报一遍\n    - 拖拽感，能够生成各种报告\n      - ISC 成果\n    - [ISC 项目 25 年 Q2 阶段性进展](https://xiaomi.f.mioffice.cn/docx/doxk4mNT77IdoPlCQuApGynhFUg)，参考 [ISC 项目 25 年 Q1 阶段性进展（20250401）](https://xiaomi.f.mioffice.cn/docx/doxk4wmSpjJ0zzPxQQt7QALHz9b)\n    - 认知上：流程上线、标准化、简介\n    - 交付流：全球多场景、多品类的计划大脑，未来支撑空调出海等，配上 AISC 的构想\n      - 品类管理的纵深足够深，对比行业；条码从代工厂走到了售后\n    - 成本流：如何体现管理方法要先进？预测线、实结线\n- #PMO 共创\n\n  - [ISC 如何做更主动的项目管理](https://xiaomi.f.mioffice.cn/docx/doxk4vpXevjNmdZnrsaDPUoZJuX)\n- #AI 专项\n\n  - 信息流的原则\n    - 信息加工链路是单一的\n    - 主场景是飞书文档，非必要不改变用户习惯，要符合用户直觉\n    - 把 supertag 和 tag 区分开\n"}], null]